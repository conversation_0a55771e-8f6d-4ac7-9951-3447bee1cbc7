<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width,initial-scale=1.0">
  <link rel="icon" id="icon" href="">
  <title></title>
  <style>
    #jt-app-loading {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translateY(-50%) translateX(-50%);
    }
  </style>

  <script>
    function choosePlatform() {
      var params = new URL(window.location.href).searchParams;
      var entrance = params.get('entrance');
      var paramsList = {entrance: entrance}
      return paramsList
    }
    var currentPlatform = choosePlatform().entrance;
    var obj = document.getElementById('icon');
    //根据不同的平台来源展示不同title和favicon,再有拓展的平台在这里加

    // document.title = '九天大模型开发平台';
    // obj.href = '<%= BASE_URL %>favicon.ico';

    switch (currentPlatform) {
      case 'sasac':
        document.title = '央企AI焕新服务平台';
        obj.href = '<%= BASE_URL %>favicon_sasac.png';
        break;
      default:
        document.title = '九天大模型开发平台';
        obj.href = '<%= BASE_URL %>favicon.ico';
    }
  </script>
</head>

<body>
  <noscript>
    <strong>We're sorry but the page doesn't work properly without JavaScript enabled.
      Please enable it to continue.</strong>
  </noscript>
  <div id="jt-app-loading">
    <img src="<%= BASE_URL %>loading-icon.gif" width="412" alt="" />
  </div>
  <div id="app"></div>
  <!-- built files will be auto injected -->
</body>

</html>