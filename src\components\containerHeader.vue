<template>
  <div>
    <a-flex class="container-header" :align="props.flexAlign" :justify="props.flexJustify">
      <h2>{{ props.title }}</h2>
      <slot name="extra"></slot>
    </a-flex>
    <a-divider style="margin: 0" />
  </div>
</template>

<script setup>
const props = defineProps({
  flexAlign: {
    type: String,
    default: 'center',
  },
  flexJustify: {
    type: String,
    default: 'space-between',
  },
  title: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.container-header {
  margin-bottom: 16px;
}
</style>
