import { STORAGE_DIR_TABS_MAP } from '@/constants/storageDirManager';

// 运管中心-存储目录管理
const STORAGE_DIR_MANAGER = [
  {
    path: '/storage-dir-manager',
    name: 'storage-dir-manager',
    component: () => import('@/views/management-center/storage-dir-manager/index.vue'),
    children: [
      //存储目录管理列表
      {
        path: '',
        name: 'storage-dir-manager-list',
        component: () => import('@/views/management-center/storage-dir-manager/list/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '存储目录管理',
            },
          ],
        },
      },
      {
        path: 'project-detail',
        name: 'storage-dir-manager-project-detail',
        component: () => import('@/views/management-center/storage-dir-manager/project-detail/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '存储目录管理',
              path: '/storage-dir-manager',
              query: {
                tab: STORAGE_DIR_TABS_MAP.PROJECT,
              },
            },
            {
              name: '项目空间存储目录详情',
            },
          ],
        },
      },
      {
        path: 'external-detail',
        name: 'storage-dir-manager-external-detail',
        component: () => import('@/views/management-center/storage-dir-manager/external-detail/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '存储目录管理',
              path: '/storage-dir-manager',
              query: {
                tab: STORAGE_DIR_TABS_MAP.EXTERNAL,
              },
            },
            {
              name: '挂载外部存储目录详情',
            },
          ],
        },
      },
    ],
  },
];

export default STORAGE_DIR_MANAGER;
