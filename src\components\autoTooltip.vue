<template>
  <a-tooltip :open="isTooltipVisible" :placement="placement" v-bind="$attrs">
    <div ref="tooltipTrigger" :class="[customClass, 'overflow-check']" @mouseenter="showToolTip" @mouseleave="hideToolTip">
      <slot></slot>
    </div>
  </a-tooltip>
</template>

<script setup>
import { ref, defineProps } from 'vue';

const props = defineProps({
  maxWidth: { type: String, default: '0' },
  class: { type: String, default: '' },
  placement: { type: String, default: 'top' },
});
const isTooltipVisible = ref(false);
const tooltipTrigger = ref(null);
const customClass = ref(props.class);

const showToolTip = () => {
  const targetElement = tooltipTrigger.value;

  // 获取内部元素的实际高度
  const innerElement = targetElement.querySelector('.ellipsis-element');
  if (!innerElement) {
    isTooltipVisible.value = true;
    return;
  }

  // 文字溢出，显示Tooltip
  if (innerElement.offsetWidth > props.maxWidth) {
    isTooltipVisible.value = true;
  } else {
    // 文字没有溢出，隐藏Tooltip
    isTooltipVisible.value = false;
  }
};

const hideToolTip = () => {
  // 鼠标移开时隐藏Tooltip
  isTooltipVisible.value = false;
};
</script>

<style scoped>
.overflow-check {
  width: 100%;
  overflow: hidden;
  text-wrap: nowrap;
  text-overflow: ellipsis;
}
</style>
