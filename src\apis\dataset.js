// 数据集相关接口
import { requestWithProjectId } from '@/request';
const { POST } = requestWithProjectId;

// 根据数据集分类数据类型查询数据集
export const getDatasetList = (data) => POST('/web/dataset/v1/external/query', data);
// 获取自定义数据集查询接口
export const getDatasetCustomList = (data) => POST('/web/dataset/v1/external/queryDataset', data);

// TODO: 接口定义重复，后续需要统一一下
// 查询预置数据集
export const getDatasetPresetList = (data) => POST('/web/dataset/v1/external/preset/query', data);
// 查询分享的自定义数据集版本信息
export const getDatasetShareList = (data) => POST('/web/dataset/v1/external/query/share/versionIds', data);
// 查询非分享的自定义数据集版本信息
export const getDatasetNoShareList = (data) => POST('/web/dataset/v1/external/query/noShare/versionIds', data);
//流模式保存数据集
export const syncStreamVersion = (data) => POST('/web/dataset/v1/syncStreamVersion', data);
//数据集版本占用模块  -> 流模式保存数据集前置条件
export const occupiedQuery = (data) => POST('/web/dataset/v1/occupied/query', data);
