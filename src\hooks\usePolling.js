import { ref, onBeforeUnmount } from 'vue';

export default function usePolling(fetchData, options = {}) {
  const {
    interval = 3000, // 轮询间隔时间，默认为 3000 毫秒
    maxCount = 10, // 最大轮询次数，默认为 10
    onData = (data) => data, // 处理轮询数据的回调函数，默认直接返回数据
    onError = (error) => console.warn('Error fetching data:', error), // 处理轮询错误的回调函数，默认打印错误信息
    step = 2, // 每次轮询间隔时间增加的步长，单位为秒，默认为 2 秒
  } = options;

  const data = ref(null);
  let timer;
  let count = 0;
  let currentInterval = interval; // 声明 currentInterval 变量并初始化为 interval

  const startPolling = (params) => {
    if (typeof fetchData !== 'function') {
      throw new Error('fetchData must be a function');
    }

    const poll = async () => {
      if (count >= maxCount) {
        stopPolling();
        return;
      }
      try {
        const response = await fetchData(params);
        data.value = onData(response.data);
      } catch (error) {
        onError(error);
        stopPolling(); // 出现错误时停止轮询
      }
      count++; // 在成功获取数据后递增计数器
      currentInterval = interval + step * count * 1000; // 根据 step 参数增加间隔时间
      clearTimeout(timer); // 清除旧的定时器
      timer = setTimeout(poll, currentInterval); // 使用更新后的间隔时间设置新的定时器
    };
    count = 0;
    poll(); // 立即执行第一次轮询
  };

  const stopPolling = () => {
    count = maxCount + 1;
    clearTimeout(timer);
  };

  const stopPollingOnLeave = () => {
    stopPolling();
  };

  onBeforeUnmount(() => {
    stopPolling();
  });

  return {
    data,
    startPolling,
    stopPollingOnLeave,
  };
}
