export const SERVICE_MAP = {
  instance: '在线服务',
  group: '服务群组',
};
export const SERVICE_TAB_LIST = [
  { tab: '1', label: SERVICE_MAP.instance, value: 'instance' },
  { tab: '2', label: SERVICE_MAP.group, value: 'group' },
];

export const columns = [
  {
    title: '应用名称',
    dataIndex: 'appName',
    key: 'appName',
    width: 250,
  },
  {
    title: 'AppCode',
    dataIndex: 'appCode',
    key: 'AppCode',
    width: 250,
  },
  {
    title: '描述',
    dataIndex: 'description',
    key: 'description',
    width: 220,
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: true,
    width: 180,
  },
  {
    title: '操作',
    key: 'option',
    width: 120,
  },
];

export const innerGroupColumns = [
  {
    title: '服务名称',
    dataIndex: 'name',
    key: 'name',
    width: 445,
  },
  {
    title: '类型',
    dataIndex: 'appType',
    key: 'appType',
    width: 200,
    filters: SERVICE_TAB_LIST.map((item) => ({ text: item.label, value: item.value })),
  },
  {
    title: '关联时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 180,
  },
  {
    title: '操作',
    key: 'option',
    width: 120,
  },
];
