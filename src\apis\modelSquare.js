import { requestWithProjectId } from '@/request/index';
const { POST, GET } = requestWithProjectId;
// 获取模型广场列表
export const getPresetModelList = (data) => POST('/web/model/manage/preset/v1/page-list', data);
// 获取模型广场类别数量
export const getModelTypeCount = (data) => GET('/web/model/manage/preset/v1/count', data);
// 获取模型详情
export const getModelDetail = (data) => GET('/web/model/manage/preset/v1/detail', data);
//  获取体验的配置项
export const getExperienceConfig = (data) => GET('/web/model/manage/preset/v1/config', data);
