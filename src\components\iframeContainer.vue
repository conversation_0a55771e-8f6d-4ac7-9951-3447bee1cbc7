<template>
  <div>
    <jt-loading v-if="isLoading"></jt-loading>
    <iframe :src="link" class="labelIframe" id="iframeContainer" title="" />
  </div>
</template>
<script setup>
import { useStore } from 'vuex';

defineProps({
  link: {
    type: String,
    default: '',
  },
});
const store = useStore();
const isLoading = ref(true);

onMounted(() => {
  const iframe = document.getElementById('iframeContainer') || {};
  iframe.onload = () => {
      isLoading.value = false;
      postData(iframe, store.state.token);
  };
});

const postData = (iframeDom, token) => {
  const iframe = iframeDom || document.getElementById('iframeContainer') || {};
  const iframeWindow = iframe.contentWindow;
  const data = { token };
  iframeWindow.postMessage(data, '*');
};

watch(
  () => store.state.token,
  (token) => {
    postData(undefined, token);
  }
);
</script>
<style lang="less" scoped>
.labelIframe {
  width: 100%;
  min-height: calc(100vh - 95px);
  border: none;
}
</style>