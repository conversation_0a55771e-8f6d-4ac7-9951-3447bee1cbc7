export const RESOURCE_MANAGE_TABS = {
  RESOURCE_GROUP: '1',
  NODE_GROUP: '2',
};

// 状态对照表
const STATUS = {
  onSTART: 1, // 启动中
  RUNNING: 3, // 运行中
  onSTOP: 4, // 停止中
  STOP: 5, // 已停止
  SUCCESS: 0, //成功
  FAIL: 6, // 失败
  SNAPSHOT: 7, // 快照中
  UNKNOWN: 8, //未知
};
export const getStatusMarkStyle = (status) => {
  const statusClassnamemap = new Map([
    [STATUS.onSTART, 'warning'],
    [STATUS.onSTOP, 'warning'],
    [STATUS.SNAPSHOT, 'warning'],
    [STATUS.RUNNING, 'processing'],
    [STATUS.FAIL, 'error'],
    [STATUS.SUCCESS, 'complete'],
    [STATUS.STOP, 'default'],
    [STATUS.UNKNOWN, 'unknown'],
  ]);
  if (!statusClassnamemap.has(+status)) {
    return +status;
  }
  return statusClassnamemap.get(+status);
};
export const getStatusMarkWord = (status) => {
  const statusWordmap = new Map([
    [STATUS.onSTART, '启动中'],
    [STATUS.onSTOP, '停止中'],
    [STATUS.RUNNING, '运行中'],
    [STATUS.FAIL, '失败'],
    // [STATUS.SUCCESS, '成功'],
    // [STATUS.STOP, '停止'],
    [STATUS.SNAPSHOT, '快照中'],
    [STATUS.UNKNOWN, '未知'],
  ]);
  if (!statusWordmap.has(+status)) {
    return +status;
  }
  return statusWordmap.get(+status);
};

export const STATUSFILTERS = [
  { text: '启动中', value: 1 },
  { text: '运行中', value: 3 },
  { text: '停止中', value: 4 },
  // { text: '停止', value: 5 },
  // { text: '成功', value: 0 },
  { text: '失败', value: 6 },
  { text: '快照中', value: 7 },
  { text: '未知', value: 8 },
];
