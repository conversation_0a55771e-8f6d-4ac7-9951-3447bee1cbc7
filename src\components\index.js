import JtIcon from './jtIcon';
import Tag from './statusTag.vue';
import Pagination from './pagination.vue';
import Empty from './empty.vue';
import EmptyData from './emptyData.vue';
import ListBanner from './listBanner.vue';
import ReloadIconBtn from './reloadIconBtn.vue';
import Container from './container.vue';
import ContainerItem from './containerItem.vue';
import SearchInput from './searchInput.vue';
import Loading from './Loading.vue';
import PartLoading from './partLoading.vue';
import DataLoading from './dataLoading.vue';
import Xterminal from './xTerminal.vue';
import SocketLog from './socketLog.vue';
import SubHeader from './subHeader.vue';
import WelcomePage from './welcomePage.vue';
import JtTag from './jtTag.vue';
import MicroComponents from './micro-components/index.vue';
import JtConfirmModal from './confirmModal/index.vue';
import TagsInput from './tagsInput.vue';
import ContainerHeader from './containerHeader.vue';
import IframeContainer from './iframeContainer.vue';
import jtDotTag from './jtDotTag.vue';

export default {
  // eslint-disable-next-line no-unused-vars
  install: (app, options) => {
    app.component('JtIcon', JtIcon);
    app.component('JtStatusTag', Tag);
    app.component('JtPagination', Pagination);
    app.component('JtEmpty', Empty);
    app.component('JtEmptyData', EmptyData);
    app.component('JtListBanner', ListBanner);
    app.component('JtReloadIconBtn', ReloadIconBtn);
    app.component('JtContainer', Container);
    app.component('JtContainerItem', ContainerItem);
    app.component('JtSearchInput', SearchInput);
    app.component('JtLoading', Loading);
    app.component('JtPartLoading', PartLoading);
    app.component('JtDataLoading', DataLoading);
    app.component('JtXterminal', Xterminal);
    app.component('JtSocketLog', SocketLog);
    app.component('JtSubHeader', SubHeader);
    app.component('JtWelcomePage', WelcomePage);
    app.component('JtTag', JtTag);
    app.component('JtMicroComponents', MicroComponents);
    app.component('JtConfirmModal', JtConfirmModal);
    app.component('JtTagsInput', TagsInput);
    app.component('JtContainerHeader', ContainerHeader);
    app.component('JtIframeContainer', IframeContainer);
    app.component('JtDotTag', jtDotTag);
  },
};
