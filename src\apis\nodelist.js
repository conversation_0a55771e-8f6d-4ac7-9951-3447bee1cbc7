import { GET, POST, requestWithPoolId } from '@/request';
const { GET: requestWithPoolIdGet, POST: requestWithPoolIdPost } = requestWithPoolId;

//获取资源组列表 /web/admin/resource/v1/manage/get-resource-group-list
export const getAllResourceList = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-resource-group-list', data);
//获取节点列表
export const getAllNodeList = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-node-list', data);
//获取节点详情
export const getNodeDetail = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/get-node-detail', data);
//查询所属集群
export const queryCluster = () => requestWithPoolIdGet('/web/admin/resource/v1/manage/cluster-info');

//获取实例列表 /web/admin/resource/v1/manage/get-pod-list
export const getInstanceList = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-pod-list', data);

//更新节点状态服务
export const updateNodeStatu = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/update-node-status', data);
//更新节点状态服务
export const getNodeStatus = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-node-status-change-record', data);