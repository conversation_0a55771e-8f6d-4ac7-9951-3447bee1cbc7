<template>
  <div id="container" :class="customClass" :style="customStyle">
    <slot></slot>
  </div>
</template>
<script>
import { defineComponent } from 'vue';

export default defineComponent({
  props: {
    customClass: String, // 自定义类名
    customStyle: Object, // 自定义行内样式
  },
  setup() {
    return {};
  },
});
</script>
<style lang="less" scoped>
#container {
  padding: 20px;
  margin-bottom: 20px;
  background-color: white;
  color: @jt-text-color-primary;
  border-radius: 4px;
  box-shadow: 0px 2px 4px 0px rgba(0, 20, 26, 0.04);
}
</style>
