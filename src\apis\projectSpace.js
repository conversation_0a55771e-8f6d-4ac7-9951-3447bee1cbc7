import { GET, POST, requestWithPoolId, requestWithProjectId } from '@/request';
const { GET: requestWithProjectIdGET, POST: requestWithProjectIdPOST } = requestWithProjectId;
const { POST: requestWithPoolIdPOST, GET: requestWithPoolIdGET } = requestWithPoolId;

// 分页查询用户参与的项目空间列表
export const getProjectSpaceList = (data) => requestWithPoolIdPOST('/web/project/v1/project/list', data);

// 编辑项目空间
export const editProjectSpace = (data, headers) => POST('/web/project/v1/project/edit', data, { headers });

// 删除项目空间
export const deleteProjectSpace = (data, headers) => GET('/web/project/v1/project/delete', data, { headers });

// 退出项目空间
export const quitProjectSpace = (data, headers) => GET('/web/project/v1/project/quit', data, { headers });

// 获取用户是否可以创建项目的状态
export const enableCreateProject = () => requestWithPoolIdGET('/web/project/v1/project/enable-create');

//获取项目详情
export const getProjectDetail = (data) => requestWithProjectIdGET('/web/project/v1/project/detail', data);
export const getAdminProjectDetail = (data) => requestWithPoolIdGET('/web/admin/project/v1/project/get-board-detail-info', data);
// 获取用户参与的所有项目列表
export const getProjectListAll = () => requestWithPoolIdGET('/web/project/v1/project/list-all');

// 获取用户最近访问的项目
export const getProjectListLastest = () => requestWithPoolIdGET('/web/project/v1/project/list-latest');

//获取成员列表
export const getProjectMemberList = (data) => requestWithProjectIdPOST('/web/project/v1/member/list', data);
export const getAdminProjectMemberList = (data) => requestWithPoolIdPOST('/web/admin/project/v1/member/list', data);
//编辑详情项目空间
export const editDetailSpace = (data) => requestWithProjectIdPOST('/web/project/v1/project/edit', data);

//编辑项目成员
export const editProjectMember = (data) => requestWithProjectIdPOST('/web/project/v1/member/edit', data);

//添加项目成员
export const addProjectMember = (data) => requestWithProjectIdPOST('/web/project/v1/member/add', data);

//删除项目详情空间
export const deleteDetailsSpace = (data) => requestWithProjectIdGET('/web/project/v1/project/delete', data);

//删除项目成员
export const deleteProjectMember = (data) => requestWithProjectIdPOST('/web/project/v1/member/delete', data);

// 退出项目详情空间
export const quitDetailSpace = (data) => requestWithProjectIdGET('/web/project/v1/project/quit', data);

// 获取用户参与的所有项目列表
export const getProjectSpaceAllList = (data) => requestWithProjectIdGET('/web/project/v1/project/list-all', data);

//校验能否添加该项目成员

export const getProjectEnablejoin = (data) => requestWithProjectIdGET('/web/project/v1/member/enable-join', data);

//获取用户当前项目中角色
export const getProjectRole = (data) => requestWithProjectIdGET('/web/project/v1/member/role', data);

// 获取角色列表
export const getRoleList = (data) => requestWithProjectIdGET('/web/project/v1/role/list', data);
export const getAdminRoleList = (data) => requestWithPoolIdGET('/web/admin/project/v1/role/list', data);
//获取当前项目配额使用情况
export const getQuotaStatus = (data) => requestWithProjectIdGET('/web/project/v1/quota/status', data);
export const getAdminQuotaStatus = (data) => requestWithPoolIdGET('/web/admin/project/v1/quota/status', data);
// 添加项目访问记录
export const visitProject = () => requestWithProjectIdGET('/web/project/v1/project/visit');
//获取关联资源组修改
export const getRsgroupEdit = (data) => requestWithProjectIdPOST('/web/project/v1/resgroup/edit', data);

// 获取关联资源组id及引用状态
export const getAssociatedResourceGroup = (data) => requestWithProjectIdGET('/web/resource/v1/project/resource-group-status', data);

// 获取在途申请的扩容工单
export const getPendingExpandOrder = (data) => requestWithProjectIdGET('/web/project/v1/quota/expand/processing', data);

// 撤销在途申请的扩容工单
export const cancelPendingExpandOrder = (data) => requestWithProjectIdGET('/web/project/v1/quota/expand/cancel', data);

// 配额扩容申请
export const applyExpandQuota = (data) => requestWithProjectIdPOST('/web/project/v1/quota/expand/request', data);

// 获取资源池配置信息
export const getPoolPolicy = (data) => requestWithPoolIdGET('/web/project/v1/pool/policy', data);

// 查看资源组详情页时按条件分页查询某个资源池下关联项目列表
export const listRelatedProject = (data) => requestWithPoolIdPOST('/web/admin/project/v1/pool/list-related-project', data);

// 编辑资源组时全量查询某个资源池下有关联状态的项目列表
export const listAllRelatedProject = (data) => requestWithPoolIdGET('/web/admin/project/v1/pool/list-all-related-project', data);

// 运管侧-获取项目访问策略（判断对应项目是否支持编辑读写权限）
export const getAdminProjectPolicy = (data) => requestWithPoolIdGET('/web/admin/project/v1/project/policy', data);

//项目空间详情资源看板
export const getResourceBoard = (data) => requestWithProjectIdGET('/web/resource/v1/project/quota-usage-summary', data);
export const getAdminResourceBoard = (data) => requestWithPoolIdPOST('/web/admin/resource/v1/board/get-quota-usage-summary', data);
export const getAdminGpUBoard = (data) => requestWithPoolIdPOST('/web/admin/resource/v1/board/get-gpu-usage-summary', data);
//项目空间资源组详情资源看板
export const getResourDetaileBoard = (data) => requestWithProjectIdPOST('/web/resource/v1/private-resource-group/get-detail-used-rate-graph', data);
export const getResourUsedBoard = (data) => requestWithProjectIdPOST('/web/resource/v1/private-resource-group/get-resource-used-rate-graph', data);

// 获取项目配置信息
export const getProjectPolicy = (data) => requestWithProjectIdGET('/web/project/v1/project/policy', data);

//获取允许用户申请新建项目权限的资源池信息GET
export const getCreatPoolinfo = (data) => GET('/web/resource/v1/pool-info-allowed-create', data);

//申请新建项目空间配额
export const userQuotaRequest = (data) => POST('/web/project/v1/quota/user/request', data);

//获取在途申请新建项目空间
export const getProcessing = (data) => GET('/web/project/v1/quota/user/processing', data);

//撤销在途申请新建空间配额
export const getuserCanel = (data) => GET('/web/project/v1/quota/user/cancel', data);

// 查询用户某项配额值
export const getUserQuery = (data) => requestWithProjectIdGET('/web/project/v1/quota/user/query', data);

// 获取总共资源使用统计
export const getProjectUsageTotal = (data) => requestWithProjectIdPOST('/web/project/v1/stat/project-usage-total', data);
export const getProjectUsageList = (data) => requestWithProjectIdPOST('/web/project/v1/stat/list-project-act-usage', data); // 获取项目资源统计列表
