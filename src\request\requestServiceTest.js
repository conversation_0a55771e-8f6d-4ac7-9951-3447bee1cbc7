import Axios from 'axios';

export const axios = Axios.create({
  // withCredentials: true, // set cross-origin
  headers: {
    'Content-Type': 'application/json',
  },
  // crossDomain: true,
});

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (err) => {
    throw err;
    // return err;
  }
);

function request(url, options) {
  const { method = 'POST', params = {}, data = {}, headers = {} } = options || {};
  return axios({
    url,
    method,
    params,
    data,
    headers: {
      ...headers,
    },
    // crossDomain: true,
  })
    .then((res) => {
      return res;
    })
    .catch((err) => {
      throw err;
      // return err;
    });
}

let abort = new AbortController();
// 流式请求数据
export const fetchSteam = (url, method, params, data, headers) => {
  let newUrl = url;
  if (Object.keys(params)?.length) {
    const queryParams = new URLSearchParams(params).toString();
    newUrl = `${url}?${queryParams}`;
  }
  return fetch(newUrl, {
    method,
    headers: {
      ...headers,
    },
    body: data,
    signal: abort.signal,
    // credentials: 'include',
  })
    .then((response) => {
      // 针对400或者500的错误处理，fetch在400或者500不抛出错误
      if (response?.status >= 200 && response?.status < 300) {
        return response;
      }
      const error = new Error(response.statusText);
      error.response = response;
      throw error;
    })
    .catch((err) => {
      throw err;
    });
};

export function SEND(url, method, params, data, headers) {
  return request(url, { method, params, data, headers, useError: true });
}
