import { requestWith<PERSON>oolId } from '@/request';
const { GET: requestWithPoolIdGet, POST: requestWithPoolIdPost } = requestWithPoolId;

/**
 * @description:
 * @param {*} data
 * @return {*}
 */
export const getPoolUsageApi = (data) => requestWithPoolIdGet('/web/admin/storage/v1/getPoolUsage', data);

export const getComputerPowerApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/board/get-compute-power-index', data);

export const getImageQuotaApi = (data) => requestWithPoolIdPost('/web/admin/image/v1/operator/manage/get_resource_quotas', data);
