// 运管中心-运营看板
import { BOARD_EXP_VIEW_AUTH } from '@/constants/board';
const RES_DASHBOARD = [
  {
    path: '/res-dashboard',
    name: 'res-dashboard',
    component: () => import('@/views/management-center/res-dashboard/index.vue'),
    meta: {
      header: [
        {
          name: '运营看板',
        },
      ],
    },
  },
  /* 体验看板详情页 */
  {
    path: '/res-dashboard/experience-detail/:chatId',
    name: 'experience-detail',
    component: () => import('@/views/management-center/res-dashboard/experience/detail/index.vue'),
    meta: {
      admin: true,
      apvAndMgtPermission: BOARD_EXP_VIEW_AUTH,
      header: [
        {
          name: '运管中心',
        },
        {
          name: '运营看板',
          path: '/res-dashboard',
          query: {
            tab: '体验看板',
          },
        },
        {
          name: '对话详情',
        },
      ],
    },
  },
  {
    /* 与用户侧页面相似，直接复用用户侧页面，使用meta的admin属性判断 */
    path: '/res-dashboard/project-detail/:projectId',
    name: 'admin-project-detail',
    component: () => import('@/views/project-space/details/index.vue'),
    meta: {
      admin: true,
      header: [
        {
          name: '运管中心',
        },
        {
          name: '运营看板',
          path: '/res-dashboard',
        },
        {
          name: '项目空间详情',
        },
      ],
    },
  },
];

export default RES_DASHBOARD;
