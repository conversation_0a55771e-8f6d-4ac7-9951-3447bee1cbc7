import { ref, onMounted, onUnmounted } from 'vue';

export function useWindow() {
  const windowWidth = ref(window.innerWidth);

  const updateWidth = () => {
    windowWidth.value = window.innerWidth;
  };

  const onResize = (callback) => {
    const resizeHandler = () => {
      updateWidth();
      callback(windowWidth.value);
    };
    window.addEventListener('resize', resizeHandler);
    // Clean up the event listener
    onUnmounted(() => {
      window.removeEventListener('resize', resizeHandler);
    });
  };

  onMounted(() => {
    window.addEventListener('resize', updateWidth);
  });

  onUnmounted(() => {
    window.removeEventListener('resize', updateWidth);
  });

  return {
    windowWidth,
    onResize,
  };
}
