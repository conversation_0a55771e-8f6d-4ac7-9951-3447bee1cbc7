<template>
  <div id="container-wrap" :class="customClass" :style="customStyle">
    <slot>
      <Container-Item />
    </slot>
  </div>
</template>
<script>
import { defineComponent } from 'vue';
import ContainerItem from '@/components/containerItem.vue';

export default defineComponent({
  components: { ContainerItem },
  props: {
    customClass: String, // 自定义类名
    customStyle: Object, // 自定义行内样式
  },
  setup(props) {
    return {};
  },
});
</script>
<style lang="less" scoped>
#container-wrap {
  width: 100%;
  overflow-y: auto;
  padding: 20px;
}
</style>
