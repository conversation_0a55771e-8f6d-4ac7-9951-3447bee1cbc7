import request from '@/request';

// 一个vueuse的hook
export const useFetch = (url, options = {}) => {
  const data = ref(null);
  const error = ref(null);
  const loading = ref(false);

  const fetchData = async () => {
    loading.value = true;
    try {
      const res = await request(url, { ...options, data: options.data || options.params });
      data.value = res;
    } catch (err) {
      console.error(err);
      error.value = err;
    } finally {
      loading.value = false;
    }
  };

  return { data, error, loading, fetchData };
};
