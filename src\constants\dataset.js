// 数据集分类枚举
export const CLASSIFICATION_ENUM = {
  TEXT: 0,
  IMAGE: 1,
  AUDIO: 2,
  VIDEO: 3,
  MULTIMODAL: 4,
  OTHER: 5,
};

export const CLASSIFICATION_TEXT_MAP = new Map([
  [CLASSIFICATION_ENUM.TEXT, '文本'],
  [CLASSIFICATION_ENUM.IMAGE, '图像'],
  [CLASSIFICATION_ENUM.AUDIO, '音频'],
  [CLASSIFICATION_ENUM.VIDEO, '视频'],
  [CLASSIFICATION_ENUM.MULTIMODAL, '多模态'],
  [CLASSIFICATION_ENUM.OTHER, '其他'],
]);

// 数据类型枚举
export const DATASETTYPE_ENUM = {
  QA: 0,
  TEXT: 1,
  IMAGE_AND_QA: 2,
  QA_ACSD: 3,
};

export const DATASETTYPE_TEXT_MAP = new Map([
  [DATASETTYPE_ENUM.QA, '问答对'],
  [DATASETTYPE_ENUM.TEXT, '纯文本'],
  [DATASETTYPE_ENUM.IMAGE_AND_QA, '图文问答对'],
  [DATASETTYPE_ENUM.QA_ACSD, '问答对-排序'],
]);
