<template>
  <a-layout-header :class="headerClass" style="width: 100%">
    <div class="left">
      <div v-if="!isFullScreenRoute && menuDisplayCollapsed" class="trigger-space">
        <jt-icon class="trigger" :type="collapsed ? 'icondaohangzhankai' : 'icondaoh<PERSON><PERSON><PERSON><PERSON>'" @click="emit('toggleCollapsed')" />
      </div>
      <a-space class="logo-space">
        <!-- <img v-if="entrance === 'sasac'" src="@/assets/images/link/logo_sasac.png" :entrance="entrance" alt="" :class="[!homePageCanAccess && 'logo-unlink']" @click="routeToHomePage" />
        <img v-else src="@/assets/images/jiutian-word.png" :entrance="entrance" alt="" :class="[!homePageCanAccess && 'logo-unlink']" @click="routeToHomePage" /> -->
        <img :src="logoUrl" :entrance="entrance" alt="" :class="[!homePageCanAccess && 'logo-unlink']" @click="routeToHomePage" />
      </a-space>
      <select-resource-pool v-if="menuDisplayResourcePoolComputed" />
    </div>
    <div class="right"><LinkItems /></div>
    <ul class="right">
      <li v-if="menuDisplayHelpCenter && !isEntranceBySasac()" class="help-center" @click="navigateToHelpcenter"><jt-icon type="iconbangzhu" style="font-size: 16px; margin-right: 4px" /><span>帮助</span></li>
      <li v-if="menuDisplayMessageCenter && !isEntranceBySasac()"><unread-message :app-name="unreadMessageName" :show-icon="true" text="消息" :title-style="titleStyle" /></li>
      <li v-if="menuDisplayUserInfo">
        <a-dropdown placement="bottom">
          <div class="user-box" :style="isEntranceBySasac() ? { cursor: 'default' } : {}">
            <img class="avatar" :src="userInfo?.image || defaultAvatar" alt="" />
            <p :title="userInfo?.userName" style="margin: 0">{{ userInfo?.userName }}</p>
            <span v-if="!isEntranceBySasac()" class="arrow"> </span>
          </div>
          <template #overlay>
            <a-menu v-if="!isEntranceBySasac()" class="jt-login-menu">
              <a-menu-item v-if="showAccountManagement" @click="handleNavToAdmin">
                <jt-icon style="font-size: 16px; margin-right: 8px" type="iconsetting-outlined" />
                <span>账号管理</span>
              </a-menu-item>
              <a-menu-item @click="handleLogout">
                <jt-icon style="font-size: 16px; margin-right: 8px" type="iconexport" />
                <span>退出登录</span>
              </a-menu-item>
            </a-menu>
          </template>
        </a-dropdown>
      </li>
    </ul>
  </a-layout-header>
</template>

<script setup>
import { useRoute } from 'vue-router';
import { computed, defineProps, defineEmits, onMounted, unref } from 'vue';
import { useStore } from 'vuex';
import selectResourcePool from '@/components/selectResourcePool.vue';
import UnreadMessage from '@/components/unreadMessage';
import { logout } from '../keycloak';
import { updateUrlAndRefresh, openInNewTab } from '@/utils/index';
import defaultAvatar from '../assets/images/avatar_big.png';
import { getRedirectUrlPrefix } from '@/utils/index';
import useScrollHeight from '@/hooks/useScrollHeight';
import { homePageUrl, showAccountManagement } from '@/config';
import { isEntranceBySasac } from '@/utils';
import { recordUserVisit } from '@/apis/board';
import debounce from 'lodash/debounce';

const urlPrefix = getRedirectUrlPrefix();
const { scrollHeight } = useScrollHeight();
const homePageCanAccess = homePageUrl !== 'null'; //左上角的logo是否可以点击跳转

const props = defineProps({
  collapsed: {
    type: Boolean,
    required: true,
  },
  menuDisplayCollapsed: {
    type: Boolean,
    default: false,
  },
  menuDisplayResourcePool: {
    type: Boolean,
    default: undefined,
  },
  menuDisplayMessageCenter: {
    type: Boolean,
    default: true,
  },
  menuDisplayHelpCenter: {
    type: Boolean,
    default: true,
  },
  menuDisplayUserInfo: {
    type: Boolean,
    default: true,
  },
  unreadMessageName: {
    type: String,
    default: undefined,
  },
});

const isFullScreenRoute = ref(false);
const headerClass = ref('header');
const route = useRoute();

const emit = defineEmits(['toggleCollapsed']);
const store = useStore();
const userInfo = computed(() => store.state.userInfo);
const linkUrl = ref('');
const logoUrl = ref(require('@/assets/images/jiutian-word.png'));

// 创建一个计算属性，用于实时获取滚动条高度
const currentScrollHeight = computed(() => scrollHeight.value);
const menuDisplayResourcePoolComputed = computed(() => {
  if (props.menuDisplayResourcePool !== undefined) {
    return props.menuDisplayResourcePool;
  }
  return showPoolList.value || isFullScreenRoute;
});

const showPoolList = computed(() => {
  return store.state.poolList.length > 0;
});

const titleStyle = {
  height: '54px',
  'line-height': '54px',
  'font-size': '12px',
  color: 'rgba(0, 20, 26, 0.7)',
  background: 'rgba(255, 255, 255, 0.15)',
  hoverTitleColor: '#00b3cc',
};

const handleLogout = () => {
  // 清空缓存的资源池id和项目id
  updateUrlAndRefresh({ poolId: null, projectId: null }, false);
  // 退出后统一回到概览
  let redirectUrl = `${location.origin}${location.pathname}`;
  logout(redirectUrl);
};

const handleNavToAdmin = () => {
  openInNewTab(`${urlPrefix}common-management#/admin`);
};

const navigateToHelpcenter = () => {
  openInNewTab(`${urlPrefix}common-helpcenter#/homepage?platformCode=DMX_KFPT`);
};

const routeToHomePage = () => {
  if (!homePageCanAccess) return;
  let url = linkUrl.value ? linkUrl.value : homePageUrl; // 自定义跳转时优先自定义跳转
  window.location.href = url;
};

const recordVisit = debounce(() => {
  if (Boolean(store.state.recordUserVisit)) return;
  if (!unref(menuDisplayResourcePoolComputed.value)) return;
  if (['/no-auth '].includes(route.path)) return;
  store.dispatch('updateRecordUserVisit', true);
  recordUserVisit().then(() => {
    store.dispatch('updateRecordUserVisit', false);
  });
}, 1500);
onMounted(() => {
  isFullScreenRoute.value = route.meta.fullscreen;
  recordVisit();
});

onBeforeMount(async () => {
  if (isEntranceBySasac()) {
    logoUrl.value = require('@/assets/images/link/logo_sasac.png');
    linkUrl.value = computed(() => store.state.layoutConfig.link).value;
  }
});

// 监听滚动条高度的变化，实时打印
watch(currentScrollHeight, (newValue) => {
  if (newValue > 10) {
    headerClass.value = 'header white-bg';
  } else {
    headerClass.value = 'header';
  }
});

watch(
  () => route.meta.fullscreen,
  (value) => {
    isFullScreenRoute.value = value;
  }
);
</script>

<style lang="less" scoped>
.header {
  position: fixed;
  display: flex;
  height: 50px;
  padding: 0 32px 0 0;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1px;
  background: rgba(255, 255, 255, 0.5);
  box-shadow: 0px 1px 0px 0px rgba(0, 92, 164, 0.06);
  backdrop-filter: blur(8px);
  z-index: 100;
  min-width: 800px;
  .trigger-space {
    width: 50px;
    height: 50px;
    background: rgba(255, 255, 255, 0.75);
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .trigger {
    font-size: 18px;
    color: #00a0cc;
  }
  .right {
    height: 50px;
    display: flex;
    align-items: center;
    li {
      font-size: 12px;
      text-align: center;
      cursor: pointer;
      color: rgba(0, 20, 26, 0.7);
      transition: 0.3s all ease;
    }
    li:not(:last-child) {
      margin-right: 32px;
    }
    .help-center {
      height: 44px;
      align-items: center;
      display: inline-flex;
    }
    .help-center:hover {
      color: #00b3cc;
    }
  }
}
.white-bg {
  background: #fff;
}
.left {
  display: flex;
  align-items: center;
  .logo-space {
    line-height: 100%;
    margin-left: 20px;
  }
  img {
    height: 32px;
    cursor: pointer;
    &.logo-unlink {
      cursor: default;
    }
  }
  .title {
    height: 60px;
    margin-left: 8px;
    margin-right: 40px;
    font-size: 16px;
    font-weight: 600;
    color: #fff;
    cursor: pointer;
    font-family: PingFangSC-Medium, PingFang SC, sans-serif;
  }
}
.user-box {
  display: flex;
  align-items: center;
  cursor: pointer;
  height: 44px;
  .avatar {
    width: 28px;
    height: 28px;
    margin-right: 8px;
    border-radius: 50%;
  }
  p {
    font-size: 12px;
    line-height: 20px;
    max-width: 85px;
    text-overflow: ellipsis;
    overflow: hidden;
  }
}
.jt-login-menu.ant-dropdown-menu {
  /deep/.ant-dropdown-menu-item {
    color: rgb(0, 20, 26) 70%;
    background-color: transparent;
    img {
      color: rgb(7, 12, 14) 45%;
    }
    &:hover {
      color: #2bb4d6;
      background: #e6f7fa;
    }
  }
}
.arrow {
  width: 12px;
  height: 12px;
  position: relative;
  margin-left: 6px;
}

.arrow:before {
  content: '';
  position: absolute;
  bottom: 2px;
  left: 50%;
  transform: translateX(-50%);
  border-left: 4.125px solid transparent; /* 箭头的左边框 */
  border-right: 4.125px solid transparent; /* 箭头的右边框 */
  border-top: 6.25px solid rgba(0, 20, 26, 0.25); /* 箭头的上边框 */
  transition: transform 0.3s ease;
}
.user-box:hover {
  .arrow:before {
    transform: translateX(-50%) rotate(180deg); /* 悬停时向上旋转180度 */
  }
}
</style>
