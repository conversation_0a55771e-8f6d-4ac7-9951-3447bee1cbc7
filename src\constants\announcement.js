// 公告管理
export const MGT_NOTICE_VIEW_AUTH = { type: 'mgt', key: 'notice', value: 'view' };
export const MGT_NOTICE_EDIT_AUTH = { type: 'mgt', key: 'notice', value: 'edit' };
export const USR_NOTICE_EDIT_AUTH = { type: 'usr', key: 'notice', value: 'view' };
export const UPGRAD_PREVIEW_STORAGE_KEY = 'upgradPreviewContent'; // 维护页面预览内容的sessionStorage对应的key
export const POPUP_PREVIEW_STORAGE_KEY = 'popupPreviewContent'; // 公告弹窗预览内容的sessionStorage对应的key

export const ANNOUNCEMENT_TABS = {
  UPGRADE: 'upgrade',
  POPUP: 'popup',
  OVERVIEW: 'overiew',
};

// 动态状态
export const OVERVIEW_STATUS = {
  REMOVED: 0, // 下架状态
  LAUNCHED: 1, // 上架状态
};
