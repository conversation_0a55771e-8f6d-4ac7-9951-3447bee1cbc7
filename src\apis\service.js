import { requestWithProjectId } from '@/request';
const { GET: requestWithProjectIdGET, POST: requestWithProjectIdPOST } = requestWithProjectId;
// 服务列表
// 获取服务列表信息
export const getServiceList = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/list', data);
export const getAppServiceList = (data) => requestWithProjectIdGET('/web/serving/v1/instance/simple/list', data);
// 启动服务
export const startService = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/restart', data);
// 停止服务
export const stopService = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/restart', data);
// 扩缩容
export const expandStorage = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/replicas', data);
// 删除服务
export const delInstance = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/delete', data);
// 获取服务绑定的应用接入的列表
export const getBindAppList = (data) => requestWithProjectIdGET('/web/serving/v1/app/instance/info', data);
// 解绑服务接入的应用
export const unBindAppWithService = (data) => requestWithProjectIdPOST('/web/serving/v1/app/instance/unbind', data);

// 新建服务
// 镜像部署-请求平台预置镜像
export const getPresetImageList = (data) => requestWithProjectIdPOST('/web/image/v1/preset_image/preset_image_list', data);
// 镜像部署-请求自定义平台镜像
export const getCustomImageList = (data) => requestWithProjectIdPOST('/web/image/v1/custom_image/custom_image_list', data);
// 镜像部署-镜像版本
// export const getImageVersion = (data) => requestWithProjectIdPOST('/web/image/v1/custom_image/artifacts_by_version', data);
// 镜像部署-镜像版本信息
// export const getImageVersionInfo = (data) => requestWithProjectIdPOST('/web/image/v1/custom_image/artifacts_by_version', data);
// 确定url根据镜像地址查询平台预置镜像信息
export const getPresetImageListByAddress = (data) => requestWithProjectIdGET('/web/image/v1/preset_image/list_by_address', data);
// 获取预置镜像详情
export const getPresetImageDetail = (data) => requestWithProjectIdPOST(`/web/image/v1/preset_image/preset_image_info?id=${data.id}`, {});
// 获取自定义镜像详情
export const getCustomImageDetail = (data) => requestWithProjectIdGET(`/web/image/v1/custom_image/artifact`, data);
// 查询镜像服务访问方式配置
export const getPresetBodyContentByImageName = (data) => requestWithProjectIdPOST('/web/image/v1/preset_image/list_by_scene_inference', data);
// 模型部署-获取预置模型
export const getPresetModel = () => requestWithProjectIdPOST('/web/model/manage/preset/v1/list-all', {});
// 模型部署-获取自定义模型
export const getCustomModel = (data) => requestWithProjectIdPOST('/web/model/v1/manage/list', data);
// 模型部署-模型选择-获取模型版本下拉列表
export const getModelVersion = (data) => requestWithProjectIdPOST('/web/model/v1/manage/version/list', data);
// 获取模型部署的版本信息
export const getModelVersionDeployData = (data) =>
  requestWithProjectId('/web/model/v1/deploy/info', {
    method: 'POST',
    data,
    useError: true,
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
// 校验服务名称是否重复
export const checkServiceNameExit = (data) => requestWithProjectIdGET('/web/serving/v1/instance/duplicate', data);
// 服务部署提交数据接口
export const submitService = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/deploy', data);
// 获取平台预置模型的详情信息
export const getPresetModelDetail = (data) => requestWithProjectIdGET('/web/model/manage/preset/v1/detail', data);
// 获取模型关于资源的配置信息
export const getPresetModelImageResource = (data) => requestWithProjectIdGET('/web/model/manage/preset/v1/config', data);
// 通过镜像获取平台预置模型
export const getModelListByImage = (data) => requestWithProjectIdGET('/web/model/manage/preset/v1/list-by-image', data);

// 详情页
// 获取详情页信息
export const getDetail = (data) => requestWithProjectIdGET('/web/serving/v1/instance/detail', data);
// 获取pod列表
export const getPodList = (data) => requestWithProjectIdGET('/web/serving/v1/instance/pods', data);
// 删除pod
export const delPod = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/pod/delete', data);
// 查看事件
export const getPodEventsData = (url, data) => requestWithProjectIdPOST(url, data);
// 获取服务日志内容
export const getServiceLog = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/log', data);
// 在线调试获取预置请求体
export const getPresetBodyContentByAddress = (data) => requestWithProjectIdGET('/web/image/v1/preset_image/list_by_address_inference', data);
// 获取项目外调用地址的ip
export const getProjectAddress = () => requestWithProjectIdGET(`/web/serving/v1/callBackEnv/info`);

// 编辑页
// 提交编辑结果
export const submitEditService = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/update', data);
// 获取websocket地址
export const getWSUrl = (data) => requestWithProjectIdPOST('/web/serving/v1/instance/ws/url', data);
//获取文件普通存储目录和外部存储请求接口
export const getTreeFileUrl = () => requestWithProjectIdGET('/web/storage/v1/fileServiceUrl');
//获取文件外部存储目录的请求接口
export const getTreeExtendFileUrl = () => requestWithProjectIdGET('/web/admin/storage/v1/fileExternalList');

// 服务指标 获取运行实例数等数据
export const getRunningPods = (data) => requestWithProjectIdPOST('/web/serving/v1/service_performance', data);

// 服务指标 获取RT响应时间
export const getResponseTime = (data) => requestWithProjectIdPOST('/web/serving/v1/service_response_time', data);

// 服务指标 获取网络流量
export const getNetFlow = (data) => requestWithProjectIdPOST('/web/serving/v1/service_flow', data);

// 服务指标 获取服务调用情况
export const getServiceRequest = (data) => requestWithProjectIdPOST('/web/serving/v1/service_invoke', data);

// 服务指标 获取服务调用量
export const getServiceRequestCount = (data) => requestWithProjectIdPOST('/web/serving/v1/service_instant_indicators', data);

// 服务指标 获取加速卡使用情况
export const getCardUseState = (data) => requestWithProjectIdPOST('/web/serving/v1/service_gpu_indicators', data);

// 服务指标 获取服务调用明细
export const getServiceCall = (data) => requestWithProjectIdPOST('/web/serving/v1/call/statistics/instance/model/call/count', data);
// 服务指标 获取服务调用明细渠道下拉选项
export const getServiceCallOptions = (data) => requestWithProjectIdPOST('/web/serving/v1/call/statistics/instance/model/appcode', data);
//调用统计 获取框架列表
export const getFrameworkList = (data) => requestWithProjectIdGET('/web/serving/v1/call/statistics/instance/model/framework', data);

//调用统计 获取模型列表
export const getNewModelList = (data) => requestWithProjectIdGET('/web/serving/v1/call/statistics/instance/model/infos', data);

//调用统计 根据模型名获取服务列表
export const getServiceListByModel = (data) => requestWithProjectIdPOST('/web/serving/v1/call/statistics/instance/model/deployed', data);

//调用统计 获取token信息
export const getTokenInfo = (data) => requestWithProjectIdPOST('/web/serving/v1/call/statistics/instance/model/tokens', data);

// 获取平台预支模型
export const getPreModelList = () => requestWithProjectIdPOST('/web/model/manage/preset/v1/list-all');

//应用接入 创建应用
export const createApplication = (data) => requestWithProjectIdPOST('/web/serving/v1/app/create', data);

//应用接入 获取应用列表
export const getApplicationList = (data) => requestWithProjectIdPOST('/web/serving/v1/app/list', data);

//应用接入 校验应用是否重名
export const checkApplicationIsSame = (data) => requestWithProjectIdGET('/web/serving/v1/app/checkName', data);

//应用接入 查询某个应用下绑定的所有实例
export const getServiceListByApp = (data, params) => requestWithProjectIdGET(`/web/serving/v1/app/${data.appId}/instance/list`, params);

//应用接入 绑定应用
export const bundingApplication = (data) => requestWithProjectIdPOST('/web/serving/v1/app/instance/bind', data);

//应用接入 解绑应用
export const unbundingApplication = (data) => requestWithProjectIdPOST('/web/serving/v1/app/instance/unbind', data);

//应用接入 删除应用
export const delApplication = (data) => requestWithProjectIdPOST(`/web/serving/v1/app/${data.appId}/delete`);

// 服务对应的appcode
export const getAppCode = (data) => requestWithProjectIdPOST('/web/serving/v1/call/statistics/instance/model/appcode', data);

// 服务群组
// 群组列表-获取群组列表信息
export const getGroupsList = (data) => requestWithProjectIdPOST(`/web/serving/v1/service-groups/group/list`, data);
// 新建群组-判断群组名字是否重复
export const checkGroupsNameExit = (data) => requestWithProjectIdGET(`/web/serving/v1/service-groups/group/duplicate`, data);
// 新建群组-获取当前项目空间所有在线服务列表
export const getAllServiceListForProject = (data) => requestWithProjectIdPOST(`/web/serving/v1/service-groups/group/instance/list`, data);
// 新建群组-提交服务群组
export const submitNewGroups = (data) => requestWithProjectIdPOST(`/web/serving/v1/service-groups/group/create`, data);
// 群组详情-获取服务群组详细信息
export const getGroupsDetail = (data) => requestWithProjectIdGET(`/web/serving/v1/service-groups/group/detail`, data);
// 群组详情-获取该群组下的服务列表
export const getServiceInGroups = (data) => requestWithProjectIdPOST(`/web/serving/v1/service-groups/group/detail/instance/list`, data);
// 群组详情-编辑群组服务信息
export const editGroupsInfo = (data) => requestWithProjectIdPOST(`/web/serving/v1/service-groups/group/update`, data);
// 群组详情-提交流量分布
export const submitTrafficWeight = (data) => requestWithProjectIdPOST(`/web/serving/v1/service-groups/group/traffic`, data);
// 群组详情-群组移除服务
export const delServiceFromGroups = (data) => requestWithProjectIdPOST(`/web/serving/v1/service-groups/group/remove/instance`, data);
// 更新某个群组下面的服务
export const updateServiceForGroups = (data) => requestWithProjectIdPOST(`/web/serving/v1/service-groups/group/add/instance`, data);
