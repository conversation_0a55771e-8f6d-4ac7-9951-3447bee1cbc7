<template>
  <div>
    <a-empty
      :description="null"
      :image="emptyImage"
      :image-style="{
        height: '64px',
        ...imgStyle,
      }"
    >
      <p v-if="title" class="title">{{ title }}</p>
      <p v-else class="title">
        <slot name="title"></slot>
      </p>
      <p v-if="description" class="description">{{ description }}</p>
      <p v-else class="description">
        <slot name="description"></slot>
      </p>
    </a-empty>
  </div>
</template>

<script setup>
const emptyImage = require('@/assets/images/empty/empty.png');
defineProps({
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  imgStyle: {
    type: Object,
    default: () => ({}),
  },
});
</script>

<style lang="less" scoped>
.title {
  font-weight: 400;
  font-size: 14px;
  color: #00141a;
}
.description {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 20, 26, 0.45);
  line-height: 18px;
  margin-top: 8px;
}
:deep(.ant-empty-footer) {
  margin-top: 0;
}
</style>
