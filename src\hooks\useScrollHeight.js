import { ref, onMounted, onUnmounted } from 'vue';

export default function useScrollHeight() {
  const scrollHeight = ref(0);

  const updateScrollHeight = () => {
    scrollHeight.value = window.scrollY;
  };

  onMounted(() => {
    window.addEventListener('scroll', updateScrollHeight);
  });

  onUnmounted(() => {
    window.removeEventListener('scroll', updateScrollHeight);
  });

  return {
    scrollHeight,
  };
}
