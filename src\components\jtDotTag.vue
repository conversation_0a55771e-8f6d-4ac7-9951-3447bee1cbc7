<template>
  <a-space class="dot-tag">
    <div class="dot" :class="{ active: props.active }" :style="{ backgroundColor: props.color }"></div>
    <span><slot></slot></span>
  </a-space>
</template>

<script setup>
const props = defineProps({
  active: {
    type: Boolean,
    default: false,
  },
  color: {
    type: String,
    default: '',
  },
});
</script>

<style lang="less" scoped>
.dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: rgba(0, 20, 26, 0.45);
  &.active {
    background-color: #00a0cc;
  }
}
</style>
