<template>
  <div v-if="visible" class="system-announcement-wrap">
    <div class="mask"></div>
    <div class="sys-anno-content">
      <h2>{{ props.title }}</h2>
      <!-- eslint-disable vue/no-v-html -->
      <div class="sys-anno-text" v-html="props.content"></div>
      <a-flex class="options" justify="end" align="center">
        <a-space>
          <a-checkbox v-model:checked="remember">不再提醒</a-checkbox>
          <a-button class="kl-create-btn" type="primary" @click="close">知道了</a-button>
        </a-space>
      </a-flex>
    </div>
  </div>
</template>

<script setup>
import { useStore } from 'vuex';

import { setItem } from '@/utils/storageUtil';
import { SYS_POPUP_UUID, SYS_POPUP_CLOSE_UUID } from '@/constants';
const store = useStore();
const props = defineProps({
  title: {
    type: String,
    default: '系统公告标题',
  },
  content: {
    type: String,
    default: '<p>系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容系统公告内容</p>',
  },
  uuid: {
    type: [String, Number],
    default: '',
  },
});
const visible = defineModel('visible', { type: Boolean, default: false });
const remember = ref(false);

const close = () => {
  const poolId = store.state.poolInfo.id;
  visible.value = false;
  if (remember.value && props.uuid) {
    setItem(SYS_POPUP_UUID(poolId), props.uuid);
  }
  sessionStorage.setItem(SYS_POPUP_CLOSE_UUID(poolId), props.uuid);
};
</script>

<style lang="less" scoped>
.preview-mask {
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 9999999999999;
}
.mask {
  background-color: rgba(0, 0, 0, 0.45);
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
}
.sys-anno-content {
  width: 480px;
  min-height: 304px;
  background-color: #ffffff;
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08), 0px 3px 6px -4px rgba(0, 0, 0, 0.12);
  border-radius: 4px;
  background-image: url('~@/assets/images/system-announcement/bg-top.png'), url('~@/assets/images/system-announcement/bg-down.png');
  background-size: 100%;
  background-repeat: no-repeat;
  background-position: top left, bottom left;
  position: fixed;
  left: 0;
  right: 0;
  top: 31vh;
  margin: auto;
  z-index: 1000;
  padding: 40px 32px 86px;
  .sys-anno-text {
    line-height: 22px;
    word-wrap: break-word;
  }
}
h2 {
  font-weight: 600;
  font-size: 20px;
  color: @jt-primary-color;
  line-height: 28px;
  text-align: left;
  font-style: normal;
  position: relative;
  margin-bottom: 40px;
  &:before {
    content: '';
    position: absolute;
    left: 0;
    top: calc(100% + 4px);
    width: 32px;
    height: 4px;
    background: linear-gradient(90deg, #00c1dc 0%, #00aaff 100%);
    border-radius: 2px;
  }
}
.options {
  margin-top: 24px;
  width: 100%;
  position: absolute;
  bottom: 30px;
  left: 0;
}
.kl-create-btn {
  width: 96px;
  margin-right: 32px;
}
</style>
