export const ACTIVE_TYPE_OPPOSITE = {
  develop: 0,
  task: 1,
  serving: 2,
  compress: 3,
  evaluation: 4,
  sft: 5,
  dap: 6,
  clean: 7,
  analysis: 8,
  dpo: 9,
  angmentation: 11,
  kd: 12,
};
export const ACTIVE_TYPE_NEW = {
  0: '开发环境',
  1: '训练任务',
  2: '在线服务',
  3: '模型压缩',
  4: '模型评估', // 模型评估(自动)
  5: '有监督微调',
  6: '增量预训练',
  7: '数据清洗',
  8: '数据解析',
  9: '偏好对齐',
  10: '模型评估', // 模型评估(人工)
  11: '数据增强',
  12: '模型蒸馏',
};

export const ACTIVITY_TYPE_ROUTENAME_MAP = {
  0: 'activity-dev-detail',
  1: 'activity-task-detail',
  2: 'activity-service-detail',
  3: 'activity-compress-detail',
  4: 'activity-evaluation-detail', // 模型评估(自动)
  5: 'activity-sft-detail',
  6: 'activity-dap-detail',
  7: 'activity-clean-detail',
  8: 'activity-analysis-detail',
  9: 'activity-dpo-detail',
  10: 'activity-evaluation-detail', // 模型评估(人工)
  11: 'activity-angmentation-detail',
  12: 'activity-kd-detail',
};

/**
 * @description: 活动类型tag颜色
 * @return {*}
 */
export const ACTIVE_TYPE_COLOR = {
  0: 'blue',
  1: 'tag-lightblue-jt',
  2: 'green',
  3: 'purple',
  4: 'tag-oceanblue-jt',
  5: 'pink',
  6: 'orange',
  7: 'red',
  8: 'tag-watergreen-jt',
  9: 'tag-warmorange-jt',
  10: 'tag-oceanblue-jt',
  11: 'tag-flame-jt',
  12: 'tag-skyblue-jt',
};

// 需要展示的活动类型顺序，key对应的类型见 ACTIVE_TYPE_NEW
// 顺序 ： 1数据解析 2. 数据清洗 数据增强、3.开发环境 4 训练任务 5. 增量预训练 6. 有监督微调 7. 偏好对齐 8. 模型压缩 9. 模型评估 10 推理服务
export const ACTIVE_TYPE_SORT = [8, 7, 11, 0, 1, 6, 5, 9, 12, 3, 4, 2];

// 模型评估分为自动和手动
export const HUMAN_MODEL_EVALUATION_KEY = 10;
export const AUTO_MODEL_EVALUATION_KEY = 4;
