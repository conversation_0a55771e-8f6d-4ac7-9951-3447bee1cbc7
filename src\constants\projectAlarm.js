import { showEmail, showSMS } from '@/config';
const NUM_ENUM = {
  ZERO: '0',
  ONE: '1',
  TWO: '2',
  THREE: '3',
  FOUR: '4',
  FIVE: '5',
  SIX: '6',
  SEVEN: '7',
  EIGHT: '8',
  NINE: '9',
}
// 告警管理枚举（用户侧）
//列表tabs
export const PROJECT_ALARM_TABS = {
  ALARM_RECORD: '1',
  ALARM_RULE: '2',
};

export const PROJECT_ALARM_TYPE = {
  LOG: 'log',
  RULE: 'rule',
};

//触发方式
export const TRIGGER_TYPE = [
  {
    value: '1',
    label: '立即触发',
  },
  // {
  //   value: '2', label: '累计告警',
  // },
];

//触发模式
export const TRIGGER_MODEL = [
  {
    value: '1',
    label: '只告警1次',
  },
];

//事件等级
export const EVENT_LEVEL = [
  {
    value: '1',
    label: '紧急',
    text: '紧急',
  },
  {
    value: '2',
    label: '重要',
    text: '重要',
  },
  {
    value: '3',
    label: '次要',
    text: '次要',
  },
  {
    value: '4',
    label: '提示',
    text: '提示',
  },
];

// 通知对象-noticeReceiver

export const NOTICE_RECEIVER_ENUM = {
  PRO_LEADER: '1',
  PRO_ADMIN: '2',
  CREATER: '3',
  NOTICE_GROUP: '4'
}
export const NOTICE_RECEIVER = [
  {
    value: NOTICE_RECEIVER_ENUM.PRO_LEADER, label: '项目负责人',
  },
  {
    value: NOTICE_RECEIVER_ENUM.PRO_ADMIN, label: '项目管理员',
  },
  {
    value: NOTICE_RECEIVER_ENUM.CREATER, label: '创建人',
  },
];

export const NOTICE_TYPE_ENUM = {
  SMS: '1',
  EMAIL: '2',
  MSG: '3',
};

// 通知方式
export const NOTICE_TYPE = [
  {
    value: NOTICE_TYPE_ENUM.SMS,
    label: '发送短信',
    text: '发送短信',
    isShow: showSMS,
  },
  {
    value: NOTICE_TYPE_ENUM.EMAIL,
    label: '发送邮件',
    text: '发送邮件',
    isShow: showEmail,
  },
  {
    value: NOTICE_TYPE_ENUM.MSG,
    label: '发送站内信',
    text: '发送站内信',
    isShow: true,
  },
];

// 告警规则-启用状态
export const STATUS_LABEL = {
  true: '启用',
  false: '停用',
  1: '启用',
  0: '停用',
  [NUM_ENUM.ONE]: '启用',
  [NUM_ENUM.ZERO]: '停用',
};

export const STATUS_SWITCH = {
  true: 1, //启用
  false: 0, //停用
  1: true,
  0: false,
};

export const RULE_START_STATUS_OPTIONS = [
  { value: STATUS_SWITCH.true, text: '开启' },
  { value: STATUS_SWITCH.false, text: '关闭' },
];

export const RECEIVER_ROLE_OPTIONS = [
  { value: NOTICE_RECEIVER_ENUM.PRO_LEADER, label: '项目负责人' },
  { value: NOTICE_RECEIVER_ENUM.PRO_ADMIN, label: '项目管理员' },
  { value: NOTICE_RECEIVER_ENUM.CREATER, label: '创建人' },
  { value: NOTICE_RECEIVER_ENUM.NOTICE_GROUP, label: '通知组' },
]
