import { RESOURCE_TYPE, RESOURCE_TYPE_ENUM_OBJ } from '@/constants/resourceGroup';

export const POOL_MANAGE_TABS = {
  POOLLIST: '1',
};

export const POOL_MANAGE_SEARCH_PLACEHOLDER = {
  [POOL_MANAGE_TABS.POOLLIST]: '请输入资源池/集群名称',
};

export const POOL_DETAIL_TABS = {
  RESOURCE: '1', // 资源信息
  FUNINFO: '2', // 功能信息
};

export const POOL_SWITCH_STATUS = {
  1: '开启',
  0: '关闭',
  OPEN: '开',
  CLOSE: '关',
};

export const POOL_CIRCLE_COLOR = {
  1: '#00A0CC',
  0: '#00141a73',
};

export const RESOURCE_TYPE_FILTERS = [
  {
    text: RESOURCE_TYPE[RESOURCE_TYPE_ENUM_OBJ.GPU_CARD],
    value: RESOURCE_TYPE[RESOURCE_TYPE_ENUM_OBJ.GPU_CARD],
  },
  {
    text: RESOURCE_TYPE[RESOURCE_TYPE_ENUM_OBJ.CPU],
    value: RESOURCE_TYPE[RESOURCE_TYPE_ENUM_OBJ.CPU],
  },
];
export const RESOURCE_TYPE_OPTIONS = [
  {
    label: RESOURCE_TYPE[RESOURCE_TYPE_ENUM_OBJ.GPU_CARD],
    text: RESOURCE_TYPE[RESOURCE_TYPE_ENUM_OBJ.GPU_CARD],
    value: RESOURCE_TYPE_ENUM_OBJ.GPU_CARD,
  },
  {
    label: RESOURCE_TYPE[RESOURCE_TYPE_ENUM_OBJ.CPU],
    text: RESOURCE_TYPE[RESOURCE_TYPE_ENUM_OBJ.CPU],
    value: RESOURCE_TYPE_ENUM_OBJ.CPU,
  },
];

export const RESOURCE_POOL_SORT_OBJ = {
  gpuCardNodeUse: 'usedGpuCardNode',
  gpuCardUse: 'usedGpuCard',
  cpuNodeUse: 'usedCpuNode',
  cpuUse: 'usedCpu',
  updateTime: 'updateTime',
};

export const AUTH_CHECKS = [
  { auth: 'postPretrain', value: '增量预训练' },
  { auth: 'supervisedFinetune', value: '有监督微调' },
  { auth: 'preferAlign', value: '偏好对齐' },
  { auth: 'knowledgeDistillation', value: '模型蒸馏' },
  { auth: 'modelCompress', value: '模型压缩' },
  { auth: 'modelEvaluation', value: '模型评估' },
  { auth: 'inferService', value: '推理服务' },
  { auth: 'trainDev', value: '开发环境' },
  { auth: 'trainTask', value: '训练任务' },
  { auth: 'datasetAnalyse', value: '数据解析' },
  { auth: 'datasetClean', value: '数据清洗' },
  { auth: 'datasetEnhance', value: '数据增强' },
  { auth: 'appAccess', value: '应用接入' },
  { auth: 'modelManage', value: '模型管理' },
  { auth: 'imageManage', value: '镜像管理' },
  { auth: 'fileManage', value: '文件管理' },
  { auth: 'datasetManage', value: '数据集管理' },
  { auth: 'promptManage', value: '提示词管理' },
  { auth: 'usageManage', value: '用量管理' },
  { auth: 'alertManage', value: '告警管理' },
];
