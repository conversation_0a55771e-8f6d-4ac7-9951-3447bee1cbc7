import { requestWithProjectId } from '@/request/index';
const { POST, GET } = requestWithProjectId;
// 数据集字典获取
export const getDataSetType = () => GET('/web/dataset/v1/dictionary');
// 新建模型
export const createPreModel = (data) => POST('/web/model/admin/preset/v1/create', data);
// 编辑模型
export const editPreModel = (data) => POST('/web/model/admin/preset/v1/edit', data);
// 获取预置镜像列表
export const getPreMirroList = (data) => POST('/web/admin/image/v1/operator/manage/list_infer_whole_image', data);
// 获取推理服务预置镜像列表
export const getInferPreMirroList = (data) => POST('/web/admin/image/v1/operator/manage/list_infer_image', data);
// 获取模型网络数据源
export const getModelNetworkList = () => GET('/web/model/admin/preset/v1/network/list');
// 新增模型网络数据
export const addModelNetwork = (data) => POST('/web/model/admin/preset/v1/network/add', data, { 'Content-Type': 'multipart/form-data' });
// 删除模型网络数据
export const deleteModelNetwork = (data) => POST('/web/model/admin/preset/v1/network/delete', data, { 'Content-Type': 'multipart/form-data' });
// 获取模型标签数据源
export const getModelLabelList = () => GET('/web/model/admin/preset/v1/label/list');
// 新增模型标签数据
export const addModelLabel = (data) => POST('/web/model/admin/preset/v1/label/add', data, { 'Content-Type': 'multipart/form-data' });
// 删除模型标签数据
export const deleteModelLabel = (data) => POST('/web/model/admin/preset/v1/label/delete', data, { 'Content-Type': 'multipart/form-data' });
// 判断模型名称是否存在
export const checkModelName = (data) => POST('/web/model/admin/preset/v1/isPresetNameExist', data, { 'Content-Type': 'multipart/form-data' });
// 获取已分享的模型列表
export const getModelList = () => GET('/web/model/admin/preset/v1/custom/model/share/list');
// 获取已分享的模型版本
export const getModelVersionList = (data) => GET('/web/model/admin/preset/v1/custom/version/share/list', data);
// 获取各种字典列表
export const getDictList = (data) => GET('/web/model/v1/manage/dict', data);
// 获取预置模型列表数据
export const getPreModelList = (data) => POST('/web/model/admin/preset/v1/list', data);
// 预置模型上架
export const putOnGrounding = (id) => POST(`/web/model/admin/preset/v1/onShelf/${id}`);
// 预置模型下架
export const putOffGrounding = (id) => POST(`/web/model/admin/preset/v1/offShelf/${id}`);
// 预置模型删除
export const deleteModel = (id) => POST(`/web/model/admin/preset/v1/delete/${id}`);
// 获取预置模型详情
export const getModelDetail = (data) => GET('/web/model/admin/preset/v1/detail', data);
// 获取预置模型类别
export const getModelType = (data) => GET('/web/evaluate/v1/eval-model-classification', data);
// 获取教师模型
export const getTeacherModel = (data) => GET('/web/model/admin/preset/v1/teacher-model-constant', data);

// 获取预置镜像列表
export const getImageList = (data) => POST('/web/admin/image/v1/operator/manage/list_by_page', data);
// 预置镜像上下架接口
export const editImageState = (data) => POST('/web/admin/image/v1/operator/manage/preset_image_shelve', data);
// 删除预置镜像接口
export const deleteImage = (ids) => POST('/web/admin/image/v1/operator/manage/preset_image_delete', ids);
// 新建预置镜像接口
export const createImage = (data) => POST('/web/admin/image/v1/operator/manage/create_preset_image', data);
// 编辑预置镜像接口
export const editImage = (data) => POST('/web/admin/image/v1/operator/manage/edit_preset_image', data);
// 获取预置镜像详情接口
export const getImageDetailById = (data) => POST('/web/admin/image/v1/operator/manage/preset_image_info', data);

// 运管-预置提示词

// 1 预置提示词列表接口
export const promptList = (data) => POST('/web/prompt/v1/preset/manage/list', data);

// 2 预置提示词上下架接口
export const updatePromptState = (data) => POST('/web/prompt/v1/preset/manage/promptStatus/update', data);

// 3. 预置提示词模板编辑接口
export const editPrompt = (data) => POST('/web/prompt/v1/preset/manage/update', data);

// 4. 删除预置提示词
export const deletePrompt = (data) => POST('web/prompt/v1/preset/manage/delete', data);

// 5. 新建预置提示词模板
export const createPrompt = (data) => POST('web/prompt/v1/preset/manage/create', data);

// 6. 预置提示词模板详情
export const getPromptDetail = (data) => GET('/web/prompt/v1/preset/manage/detail', data);

// 7. 获取标签集合
export const getTagList = () => GET('/web/prompt/v1/preset/tag/manage/list');
// 8. 新建标签
export const createTag = (data) => POST('/web/prompt/v1/preset/tag/manage/create', data);
// 9. 删除标签
export const deleteTag = (data) => POST('/web/prompt/v1/preset/tag/manage/delete', data);

// 10. 预置提示词列表标签集合筛选集合
export const getPromptTagList = (data) => GET('/web/prompt/v1/preset/manage/tag/list', data);

// 分页获取预置模型访问权限白名单
export const getModelAccessListByPage = (data) => POST('/web/model/admin/preset/v1/access/page-list', data);

// 修改预置模型访问权限白名单
export const updateModelAccessList = (data) => POST('/web/model/admin/preset/v1/access/edit', data);

// 查询预置模型访问权限及白名单
export const getModelAccessList = (data) => GET('/web/model/admin/preset/v1/access/list-all', data);

// 获取所有项目空间列表
export const getProjectSpaceAllList = () => GET('/web/admin/project/v1/project/list-all');
