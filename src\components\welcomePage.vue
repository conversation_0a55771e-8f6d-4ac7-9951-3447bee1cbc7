<template>
  <div class="welcome-box">
    <div class="header-box">
      <div class="title">{{ data.title }}</div>
      <div class="sub-title">{{ data.subTitle }}</div>
      <a-select v-model:value="projectName" placeholder="请选择" show-search :options="options" :option-filter-prop="'label'" style="width: 244px; height: 32px"> </a-select>
      <div class="btn-group">
        <a-tooltip v-if="!canCreate" placement="top">
          <template #title>{{ errorMessage }}</template>
          <a-button disabled class="btn disabled-btn">新建项目空间</a-button>
        </a-tooltip>
        <a-button v-else class="btn new-btn" @click="createNew">新建项目空间</a-button>

        <a-button :disabled="!projectName" class="btn" :class="projectName ? 'detail-btn' : 'disabled-btn'" type="primary" @click="handleClick">{{ data.btnText }}</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex';
import { updateUrlAndRefresh } from '@/utils/index';
import { enableCreateProject } from '@/apis/projectSpace';
export default {
  props: {
    data: {
      type: Object,
      default() {
        return {
          errorMessage: '',
        };
      },
    },
  },
  emits: ['toDetail'],
  data() {
    return {
      canCreate: false,
      projectName: undefined,
      options: [],
      originOptions: [],
    };
  },
  created() {
    this.$store.dispatch('getAllProjectList');
    this.getCanCreate();
  },
  computed: {
    ...mapState(['projectList']),
  },
  watch: {
    projectList(val) {
      if (val.length > 0) {
        this.originOptions = val;
        this.options =
          this.originOptions.map((item) => {
            return {
              label: item.name,
              value: item.id,
            };
          }) || [];
        this.projectName = this.options.length > 0 ? this.options[0].value : undefined;
      }
    },
  },
  methods: {
    getCanCreate() {
      enableCreateProject().then((res) => {
        if (res.code === 0) {
          this.canCreate = true;
          return;
        }
        if (res.code === 130121) {
          this.errorMessage = '您暂无创建项目空间权限，请联系平台管理员申请';
        } else {
          this.errorMessage = '您创建的项目空间数量已达上限 请删除项目空间后再试';
        }
        this.canCreate = false;
      });
    },
    handleClick() {
      updateUrlAndRefresh({ projectId: this.projectName });
      this.$store.commit('UPDATE_MENU_HAS_PROJECT', true);
      this.$emit('toDetail');
    },
    createNew() {
      this.$router.push({
        path: '/project-space/create',
      });
    },
  },
};
</script>

<style lang="less" scoped>
.welcome-box {
  .header-box {
    width: 100%;
    height: 646px;
    background-image: url(../assets/images/welcomeBg.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    .title {
      margin-top: 184px;
      font-size: 30px;
      font-weight: 600;
      color: #121f2c;
      line-height: 42px;
    }
    .sub-title {
      width: 528px;
      text-align: center;
      margin-top: 16px;
      margin-bottom: 64px;
      font-weight: 400;
      font-size: 14px;
      color: #606972;
      line-height: 24px;
    }
    .btn-group {
      margin-top: 32px;
      width: 244px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      .btn {
        height: 32px;
        border-radius: 2px;
      }
      .new-btn {
        color: #00a0cc;
        border-color: #00a0cc;
      }
      .detail-btn {
        background: #00a0cc;
        color: #fff;
      }
      .disabled-btn {
        border-color: #d9d9d9;
        color: rgba(0, 0, 0, 0.25);
        background-color: rgba(0, 0, 0, 0.04);
      }
    }
    :deep(.ant-select-selector) {
      padding-left: 84px;
      position: relative;
      &::before {
        position: absolute;
        top: 4px;
        left: 12px;
        content: '\9879\76ee\7a7a\95f4\ff1a';
        font-size: 14px;
        font-weight: 400;
        color: #a0a6ab;
      }
    }
    :deep(.ant-select-selection-search) {
      padding-left: 70px;
    }
  }
}
</style>
