// 资源组大类（公共资源组、专属资源组）
export const RESOURCE_GROUP_TYPE = {
  PUBLIC: 'public', // 公共资源组
  PERSONAL: 'personal', // 专属资源组
};
export const RESOURCE_STATUS = {
  FREE: '空闲',
  CROWD: '拥挤',
  BUSY: '占满',
};
// 资源组类型
export const RESOURCE_TYPE = {
  CPU: 'cpu', // CPU
  GPU: 'gpucard', // 加速卡
};
export const RESOURCE_TYPE_MSG = {
  [RESOURCE_TYPE.CPU]: 'CPU',
  [RESOURCE_TYPE.GPU]: '加速卡',
};
// 模型发布类型
export const MODEL_RELEASE_TYPE = {
  VERSION: '0', // 已有模型新版本
  MODEL: '1', // 新模型
};
// 模型类型
export const MODEL_TYPE = {
  PREPARE: '0', // 预制模型
  CUSTOM: '1', // 自定义模型
};

// 运行状态
export const MODEL_COMPRESS_STATUS = {
  START: 1, // 启动中
  LINE: 2, // 排队中
  RUNNING: 3, // 运行中
  SUCCESS: 4, // 成功
  STOPING: 5, //停止中
  FAIL: 6, // 失败
  STOP: 7, // 已停止
};

export const MODEL_COMPRESS_STATUS_OPTIONS = [
  { value: MODEL_COMPRESS_STATUS.START, label: '启动中' },
  { value: MODEL_COMPRESS_STATUS.LINE, label: '排队中' },
  { value: MODEL_COMPRESS_STATUS.RUNNING, label: '运行中' },
  { value: MODEL_COMPRESS_STATUS.SUCCESS, label: '成功' },
  { value: MODEL_COMPRESS_STATUS.STOPING, label: '停止中' },
  { value: MODEL_COMPRESS_STATUS.FAIL, label: '失败' },
  { value: MODEL_COMPRESS_STATUS.STOP, label: '已停止' },
];

// 预制模型支持的操作
export const PRESET_MODEL_SUPPORT = {
  INCRE_PRETRAIN: '1',
  FINE_TUNING: '2',
  COMPRESS: '3',
  EVALUATION: '4',
  ARRANGE: '5'
}
