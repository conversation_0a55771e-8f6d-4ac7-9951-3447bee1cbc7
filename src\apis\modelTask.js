import { requestWithProjectId } from '@/request/index';
const { POST, GET } = requestWithProjectId;

//获取基础模型
// export const getPretrainList = (data) => GET('/web/finetune/v1/inc-pretrain/model/list', data);
export const getPretrainList = (data) => POST('/web/model/manage/preset/v1/list-all', data);

//获取有监督微调基础模型
// export const getSftList = (data) => GET('/web/finetune/v1/sft/model/list', data);
export const getSftList = (data) => POST('/web/model/manage/preset/v1/list-all', data);

//获取偏好对齐基础模型
export const getDpoList = (data) => POST('/web/model/manage/preset/v1/list-all', data);
