export const PROMPT_ENGINEERING_TABS = {
  PROMPT_TEMPLATE: '1',
  PROMPT_OPTIMIZE: '2',
};

export const PROMPT_TEMPLATE_TYPE = {
  PRESET: 'preset',
  CUSTOM: 'custom',
};

export const PROMPT_TEMPLATE_MSG = {
  [PROMPT_TEMPLATE_TYPE.PRESET]: '预置模版',
  [PROMPT_TEMPLATE_TYPE.CUSTOM]: '自定义模版',
};

export const PROMPT_MODAL_ACTION = {
  ADD: 'add',
  EDIT: 'edit',
  SAVE_AS: 'saveAsTemp',
  DETAIL: 'detail',
};

export const PROMPT_MODAL_TITLE = {
  [PROMPT_MODAL_ACTION.ADD]: '新建模板',
  [PROMPT_MODAL_ACTION.EDIT]: '编辑模板',
  [PROMPT_MODAL_ACTION.SAVE_AS]: '另存为模板',
  [PROMPT_MODAL_ACTION.DETAIL]: '模板详情',
};

export const PROMPT_MODAL_BUTTON = {
  [PROMPT_MODAL_ACTION.ADD]: '立即新建',
  [PROMPT_MODAL_ACTION.EDIT]: '确认',
  [PROMPT_MODAL_ACTION.SAVE_AS]: '立即保存',
  [PROMPT_MODAL_ACTION.DETAIL]: '无',
};

export const tagColor = (title) => {
  const colorMap = {
    办公助手: 'green',
    数据分析: 'orange',
    研究资讯: 'blue',
    趣味生活: 'purple',
    移动办公: 'oceanblue-jt',
  };
  if (title && colorMap[title]) {
    return colorMap[title];
  }
  return 'default';
};

