import { colorMap, echartsCommonOptions } from '../utils';

export const getDataSeries = ({ chartData, chartDataMax, chartTitle, chartXAxisData, chartAxisLabelFormatter, chartTooltipNeedTitle, chartColorMap }) => {
  const getLinearColor = (index) => {
    const color = chartColorMap[0] ? chartColorMap : colorMap;
    return {
      x: 0,
      y: 0,
      x2: 0,
      y2: 1,
      colorStops: [
        {
          offset: 0,
          color: color[index].replace('1)', '0.15)'), // 100% 处的颜色
        },
        {
          offset: 1,
          color: color[index].replace('1)', '0.05)'), // 0% 处的颜色
        },
      ],
      global: false, // 缺省为 false
    };
  };
  const series = [];
  for (const i in chartData) {
    series[i] = {
      name: chartData[i].name,
      data: chartData[i].data,
      type: 'line',
      areaStyle: { color: getLinearColor(i) },
      smooth: true, // 设置为圆滑曲线
      showSymbol: false,
    };
  }

  const option = {
    title: {
      text: chartTitle,
      left: 0,
      textStyle: {
        rich: {
          title: {
            color: 'rgba(0, 5, 18, 0.85)',
            fontSize: 14,
          },
          subTitle: {
            color: 'rgba(0, 20, 26, 0.45)',
            padding: [0, 0, 0, 4],
            fontSize: 14,
          },
        },
      },
    },
    xAxis: {
      type: 'category',
      data: chartXAxisData,
      boundaryGap: false, // 数据点在坐标轴起点和终点处不留空隙
      min: 'dataMin', // x轴起点设置为数据中最小的时间值
      max: 'dataMax',
      axisTick: {
        alignWithLabel: true,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 20, 26, 0.7)',
        },
      },
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: chartAxisLabelFormatter,
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(0, 20, 26, 0.7)',
        },
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          show: false,
        },
      },
      padding: 12,
      // alwaysShowContent: true,
      formatter: (params) => {
        let rowString = ``;
        if (chartTooltipNeedTitle) {
          const extractContent = (input) => {
            const regex = /\{[^|]+\|([^}]+)\}/;
            const match = input.match(regex);
            return match ? match[1] : input;
          };
          rowString += `<p class='tooltip-title'>${extractContent(chartTitle)}</p>`;
        }
        // 获取百分号（%）
        const extractPercentageSymbol = (chartAxisLabelFormatter) => {
          const input = chartAxisLabelFormatter + '';
          const regex = /['"]%['"]/;
          const match = input.match(regex);
          return match ? match[0].replace(/['"]/g, '') : null;
        };
        // 获取单位（GB）
        const extractContentUnit = (input) => {
          const regex = /\{[^|]+\|[^\{\}]*[（(]([^）)]+)[）)]\}/g;
          let match;
          const results = [];
          while ((match = regex.exec(input)) !== null) {
            results.push(match[1]);
          }
          return results[0];
        };
        params.forEach((x, i) => {
          rowString += `<div class="row">
              <p>
                <span class="dot-common" style="background-color:${colorMap[i]}"></span>
                <span>${x.seriesName}</span>
              </p>`;
          if (extractContentUnit(chartTitle)) {
            rowString += `<p>${x.data} ${extractContentUnit(chartTitle) || ''}</p>`;
          } else {
            rowString += `<p>${x.data}${extractPercentageSymbol(chartAxisLabelFormatter) || ''}</p>`;
          }
          rowString += `</div>`;
        });
        return `
      <div class="tooltip-wrap">
        <div class="tooltip-content">
          ${rowString}
        </div>
        <div class="tooltip-footer">${params[0].axisValue}</div>
      </div>
      `;
      },
    },
    series,
    ...echartsCommonOptions,
  };
  if (chartDataMax) {
    option.yAxis.min = 0;
    option.yAxis.max = chartDataMax;
  } else if (!chartDataMax && option.series[0]?.data?.length === 0) {
    option.yAxis.min = 0;
    option.yAxis.max = 100;
  }

  return option;
};
