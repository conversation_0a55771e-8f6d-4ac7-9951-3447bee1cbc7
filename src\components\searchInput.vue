<template>
  <a-input v-model:value="searchValue" :placeholder="placeholder" @pressEnter="onEnter" @change="debounceOnSearch">
    <template #prefix>
      <jt-icon type="iconsousuo" class="search-icon" @click="onSearch" />
    </template>
  </a-input>
</template>

<script setup>
import { defineProps, defineEmits, defineModel } from 'vue';
import { debounce } from 'lodash';
const emit = defineEmits(['change', 'pressEnter', 'update:modelValue']);
const props = defineProps({
  debounce: {
    type: Boolean,
    default: true,
  },
  debounceTime: { type: Number, default: 500 },
  placeholder: { type: String },
});
const searchValue = defineModel();

const onSearch = () => {
  emit('change', searchValue.value);
};

const onEnter = () => {
  emit('pressEnter', searchValue.value);
};

const debounceOnSearch = props.debounce ? debounce(onSearch, props.debounceTime) : onSearch;
</script>

<style lang="less" scoped>
.search-icon {
  cursor: pointer;
}
</style>
