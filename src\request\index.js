import Axios from 'axios';
import notification from '@/utils/notification';
import store from '@/store';

import { getProxyPrefix } from '@/config';
const proxyPrefix = getProxyPrefix();

const isDev = process.env.NODE_ENV === 'development';
const baseURL = isDev ? `/api/${proxyPrefix}` : './';

export const axios = Axios.create({
  baseURL: baseURL,
  withCredentials: true, // set cross-origin
  headers: {
    'Content-Type': 'application/json',
  },
});

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (err) => {
    console.log(err, 'errr');
    if (err.response.status === 401) {
      throw new Error('未登录');
    } else if (err.response.status === 403) {
      notification.error({
        message: `无权限`,
        description: '抱歉，无操作权限',
      });
      throw err;
    } else {
      isDev &&
        notification.error({
          message: `系统错误`,
          description: err.response.config.url,
        });
      throw err;
    }
  }
);

function request(url, options) {
  const { method = 'GET', data = {}, useError = false, needPoolId, needProjectId, headers = {}, type = {} } = options || {};
  return axios({
    url,
    method,
    params: method === 'GET' ? data : undefined,
    data: ['PUT', 'POST', 'DELETE'].includes(method) ? data : undefined,
    ...type,
    headers: {
      ...headers,
      needPoolId,
      needProjectId,
    },
  })
    .then((res) => {
      if (isDev && useError && (res.data.errorCode === '-500' || res.data.errorCode === '-503')) {
        notification.error({
          message: '系统错误',
          description: `${res.config.url}: ${res.data.errorMessage}`,
        });
        throw new Error(res.data);
      }

      if (res.headers['content-disposition']) {
        return res;
      } else {
        return res.data;
      }
    })
    .catch((err) => {
      throw err;
    });
}

export function POST(url, data, options = {}) {
  return request(url, { method: 'POST', data, useError: true, headers: options.headers });
}
export function GET(url, data, options = {}) {
  return request(url, { method: 'GET', data, useError: true, headers: options.headers });
}

export function requestBlob(url, options) {
  const { method = 'GET', data = {}, useError = true, returnType = 'url' } = options;
  return axios({
    url,
    method,
    params: method === 'GET' ? data : undefined,
    data: ['PUT', 'POST', 'DELETE'].includes(method) ? data : undefined,
    responseType: 'blob',
  })
    .then((res) => {
      if (isDev && useError && (res.data.errorCode === '-500' || res.data.errorCode === '-503')) {
        notification.error({
          message: '系统错误',
          description: `${res.config.url}: ${res.data.errorMessage}`,
        });
        throw new Error(res.data);
      }

      return returnType === 'url' ? URL.createObjectURL(new Blob([res.data])) : res.data;
    })
    .catch((err) => {
      throw err;
    });
}

// 文件上传和下载相关接口，不需要登录态
export const axiosWithoutToken = Axios.create({
  baseURL: baseURL, // 本地
  withCredentials: true, // set cross-origin
});

export const requestWithPoolId = (url, options) => request(url, { ...options, needPoolId: true });
requestWithPoolId.POST = (url, data) => requestWithPoolId(url, { method: 'POST', data, useError: true });
requestWithPoolId.GET = (url, data) => requestWithPoolId(url, { method: 'GET', data, useError: true });
requestWithPoolId.requestBlob = (url, data, type) => requestWithPoolId(url, { method: 'GET', data, type, useError: true });
requestWithPoolId.requestBlobMethod = (url, method, data, type) => requestWithPoolId(url, { method, data, type, useError: true });

export const requestWithProjectId = (url, options) => request(url, { ...options, needProjectId: true, needPoolId: true });
requestWithProjectId.POST = (url, data, headers = {}) => requestWithProjectId(url, { method: 'POST', data, useError: true, headers });
requestWithProjectId.GET = (url, data) => requestWithProjectId(url, { method: 'GET', data, useError: true });
requestWithProjectId.PUT = (url, data) => requestWithProjectId(url, { method: 'PUT', data, useError: true });
requestWithProjectId.DELETE = (url, data) => requestWithProjectId(url, { method: 'DELETE', data, useError: true });
requestWithProjectId.requestBlob = (url, data, type) => requestWithProjectId(url, { method: 'POST', data, type, useError: true });
requestWithProjectId.requestBlobMethod = (url, method, data, type) => requestWithProjectId(url, { method, data, type, useError: true });

axios.interceptors.request.use((config) => {
  if (config.headers.needPoolId) {
    config.headers.poolId = store.state.poolInfo?.id || '';
  }
  if (config.headers.needProjectId) {
    config.headers.projectId = store.state.projectId || '';
  }
  delete config.headers.needPoolId;
  delete config.headers.needProjectId;
  return config;
});

export default request;
