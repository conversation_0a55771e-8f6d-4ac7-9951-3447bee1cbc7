// useModal.js
import { ref } from 'vue';

export default function useModal() {
  const open = ref(false);
  const confirmLoading = ref(false);
  const confirmDisabled = ref(false);

  function showModal() {
    open.value = true;
    console.log('open.value', open.value);
  }

  function hideModal() {
    open.value = false;
    console.log('open.value', open.value);
  }

  function startLoading() {
    confirmLoading.value = true;
  }

  function stopLoading() {
    confirmLoading.value = false;
  }

  function enableConfirm() {
    confirmDisabled.value = false;
  }

  function disableConfirm() {
    confirmDisabled.value = true;
  }

  function toggleConfirm() {
    confirmDisabled.value = !confirmDisabled.value;
  }

  return {
    open,
    confirmLoading,
    confirmDisabled,
    showModal,
    hideModal,
    startLoading,
    stopLoading,
    enableConfirm,
    disableConfirm,
    toggleConfirm,
  };
}
