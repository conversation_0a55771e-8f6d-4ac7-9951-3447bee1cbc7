<!--
 * @Author: 
 * @Date: 2024-04-15 09:09:15
 * @LastEditTime: 2024-12-24 18:05:26
 * @LastEditors: w18826555761 <EMAIL>
 * @Description: 
 demo:
 <jt-tag>Hi</jt-tag>
 <jt-tag bordered="false">Hi</jt-tag>
 <jt-tag color="red">Hi</jt-tag>
 <jt-tag color="red" rounded="small">Hi</jt-tag>
 如果当前8种风格，不满足需求
 可以自己再定义一个"-jt"结尾的color(取名不能是CSS中标准里预定义的颜色英文，因为ant会自动加到style里的background里),
 然后配置到COLORS里，和style的class
-->
<template>
  <a-tag :color="tagColor()" :class="colorClass" :bordered="bordered" :rounded="rounded">
    <span v-if="props.status">{{ props.status }}</span>
    <slot v-else></slot>
  </a-tag>
</template>

<script setup>
const colorClass = computed(() => {
  // return props.bordered ? [props.color, 'border-has'] : [props.color, 'border-none'];
  const borderStyle = props.bordered ? 'border-has' : 'border-none';
  const roundedStyle = `rounded-${props.rounded}`;
  return [props.color, borderStyle, roundedStyle];
});

const props = defineProps({
  color: {
    type: [String, null],
    default: () => 'default',
    validator(value) {
      let COLORS_ENUM = ['red', 'green', 'orange', 'blue', 'default', 'cyan', 'tag-oceanblue-jt', 'tag-watergreen-jt', 'tag-warmorange-jt', 'tag-flame-jt', 'tag-lightblue-jt', 'orange-dark-jt', 'quete-yellow-jt', 'mirror-blue-jt', 'undeploy-gray-jt', 'tag-sky-jt', 'tag-chocolate-jt', 'tag-sliver-jt', 'tag-jupyter-jt', 'tag-vscode-jt', 'tag-skyblue-jt', null];
      COLORS_ENUM = COLORS_ENUM.concat(['tag-alarmorange-jt', 'tag-alarmred-jt', 'tag-alarmblue-jt', 'tag-alarmgreen-jt']); // 告警管理
      return COLORS_ENUM.includes(value);
    },
  },
  bordered: {
    type: Boolean,
    default: () => true,
  },
  rounded: {
    type: [String, null],
    default: () => 'large',
    validator(value) {
      const ROUNDED_ENUM = ['large', 'small', null];
      return ROUNDED_ENUM.includes(value);
    },
  },
  status: {
    type: [String, null],
    default: '',
  },
});

const tagColor = () => {
  const statusColorMap = {
    运行中: 'green',
    启动中: 'orange',
    停止中: 'orange',
    已停止: 'default',
    成功: 'green',
    失败: 'red',
    重试中: 'red',
    排队中: 'quete-yellow-jt',
    快照中: 'orange',
  };
  if (props.status && statusColorMap[props.status]) {
    return statusColorMap[props.status];
  }
  return props.color;
};
</script>

<style lang="less" scoped>
.undeploy-gray-jt {
  background: #f0f2f7;
  border-color: #c2c5cf;
  color: #c2c5cf;
}
.orange-dark-jt {
  background: #fff7e6;
  border-color: #fbc075;
  color: #f87c00;
}
.quete-yellow-jt {
  background: #fffde6;
  border-color: #f3e361;
  color: #c4a400;
}
.mirror-blue-jt {
  background: #eafbff;
  border-color: #8ddbeb;
  color: @jt-primary-color;
}
.rounded-large {
  border-radius: 12px;
}
.rounded-small {
  border-radius: 2px;
}
.border-has {
  border-width: 1px;
  border-style: solid;
}
.border-none {
  border: none;
}
.ant-tag {
  height: 22px;
  font-family: PingFangSC, PingFang SC, sans-serif;
  font-weight: 400;
  font-size: 12px;
  line-height: 22px;
  font-style: normal;
  box-sizing: content-box;
  text-align: center;
}
.ant-tag-red {
  background: #fff1f0;
  border-color: #ff8c8c;
  color: #fe3a47;
}
.ant-tag-pink {
  background: #fef2ed;
  border-color: #fbb7a5;
  color: #f53922;
}
.ant-tag-green {
  background: #e6fff7;
  border-color: #61d077;
  color: #14b038;
}
.ant-tag-orange {
  background: #fff8ea;
  border-color: #fbc075;
  color: #f87c00;
}
.ant-tag-blue {
  background: #ebf5fe;
  border-color: #6bb0fb;
  color: #0289ff;
}
.ant-tag-default {
  background: #f4f6f7;
  border-color: #cbcfd2;
  color: #606972;
}
.ant-tag-cyan {
  background: #e3fcfb;
  border: 1px solid #54d1c8;
  color: #00b3ac;
}
.ant-tag-purple {
  background: rgba(248, 234, 247, 1);
  border: 1px solid rgba(228, 156, 224, 1);
  color: rgba(181, 39, 187, 1);
}

.tag-sky-jt {
  background: rgba(234, 251, 255, 1);
  border: 1px solid rgba(141, 219, 235, 1);
  color: rgba(0, 160, 204, 1);
}
.tag-chocolate-jt {
  background: #fef2ed;
  border: 1px solid #fbb7a5;
  color: #f53922;
}
.tag-sliver-jt {
  background: rgba(0, 20, 26, 0.02);
  border: 1px solid rgba(0, 20, 26, 0.15);
  color: rgba(0, 20, 26, 0.7);
}

// 训练任务
.tag-lightblue-jt {
  background: #f3f0fe;
  border: 1px solid #c3b1fb;
  color: #7440f6;
}
// 模型评估
.tag-oceanblue-jt {
  background: #eafbff;
  border: 1px solid #8ddbeb;
  color: #00a0cc;
}
// 数据解析
.tag-watergreen-jt {
  background: #e5fef8;
  border: 1px solid #7be1d3;
  color: #00b3ac;
}
// 偏好对齐
.tag-warmorange-jt {
  background: #fffce8;
  border: 1px solid #fadc6d;
  color: #cc9213;
}
//模型蒸馏
.tag-skyblue-jt {
  background: #eceeff;
  border: 1px solid #a1a8ff;
  color: #5461ff;
}
// 数据增强
.tag-flame-jt {
  background: #fff2ea;
  border: 1px solid #f5aa8d;
  color: #ff683b;
}
// 模型蒸馏
.tag-distillation-jt {
  background: #eceeff;
  border: 1px solid #a1a8ff;
  color: #5461ff;
}
.tag-vscode-jt {
  background: #f8eaf7;
  border: 1px solid #e49ce0;
  color: #b527bb;
  height: 18px;
  line-height: 18px;
}
.tag-jupyter-jt {
  color: #ea7300;
  background-color: #fff7e8;
  border: 1px solid #f7c887;
  height: 18px;
  line-height: 18px;
}

// 告警管理
.tag-alarmorange-jt {
  background: #fff6e6;
  border: 1px solid #ffdca3;
  color: #d94e00;
}
.tag-alarmred-jt {
  background: #fff0ed;
  border: 1px solid #fcc6bd;
  color: #d51e25;
}
.tag-alarmblue-jt {
  background: #ebf5fe;
  border: 1px solid #cde6fe;
  color: #025cd5;
}
.tag-alarmgreen-jt {
  background: #e5fef8;
  border: 1px solid #c0f0eb;
  color: #009592;
}
.tag-alarmdefault-jt {
  background: #00141a05;
  border: 1px solid #00141a26;
  color: #00141a73;
}
</style>
