/*
 * @Author: <PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-02-13 17:18:17
 * @Last Modified by: <PERSON><PERSON><PERSON><PERSON>
 * @Last Modified time: 2024-05-23 14:48:29
 */
export const PROJECT_KEY = 'JTKL_PROJECT_ID_KEY';
export const POOLID_KEY = 'JTKL_POOL_ID_KEY';
export const SYS_POPUP_UUID = (poolId = '') => 'SYS_POPUP_UUID-' + poolId; // 公告弹窗对应的uuid（不再提醒对应的key）
export const SYS_POPUP_CLOSE_UUID = (poolId = '') => 'SYS_POPUP_CLOSE_UUID-' + poolId; // 公告弹窗对应的uuid（点击“知道了”对应的key，重启浏览器后重新提醒）

// 侧边栏路径方式
export const LINK_FLAG = {
  RELATIVE: 0, // 相对路径
  ABSOLUTE: 1, // 绝对路径
};

// 服务列表-服务状态
export const SERVICE_STATUS_ENUM = {
  STRAT_UP: 'starting', // 启动中
  PARTIALLY_RUNNING: 'partiallyRunning', // 部分运行中
  RUNNING: 'running', // 运行中
  STOPING: 'stopping', // 停止中
  STOP: 'stopped', // 已停止
  FAIL: 'error', // 重试中
  UPDATING: 'updating', // 更新中
};

// 服务列表-服务状态筛选值
export const SERVICE_STATUS_FILTER = [
  { text: '启动中', value: SERVICE_STATUS_ENUM.STRAT_UP },
  { text: '部分运行中', value: SERVICE_STATUS_ENUM.PARTIALLY_RUNNING },
  { text: '运行中', value: SERVICE_STATUS_ENUM.RUNNING },
  { text: '停止中', value: SERVICE_STATUS_ENUM.STOPING },
  { text: '已停止', value: SERVICE_STATUS_ENUM.STOP },
  { text: '重试中', value: SERVICE_STATUS_ENUM.FAIL },
  { text: '更新中', value: SERVICE_STATUS_ENUM.UPDATING },
];

//  服务列表-服务状态-文案
export const SERVICE_STATUS_ENUM_TEXT = new Map([
  [SERVICE_STATUS_ENUM.STRAT_UP, '启动中'],
  [SERVICE_STATUS_ENUM.PARTIALLY_RUNNING, '部分运行中'],
  [SERVICE_STATUS_ENUM.RUNNING, '运行中'],
  [SERVICE_STATUS_ENUM.STOPING, '停止中'],
  [SERVICE_STATUS_ENUM.STOP, '已停止'],
  [SERVICE_STATUS_ENUM.FAIL, '重试中'],
  [SERVICE_STATUS_ENUM.UPDATING, '更新中'],
]);

// 服务列表-资源类型筛选值
export const SERVICE_ROURCE_TYPE_ENUM = {
  CPU: 'CPU',
  GPU: 'GPU',
};

export const RESOURCE_TYPE = {
  CPU: 'cpu', // CPU
  GPU: 'gpucard', // 加速卡
};

export const RESOURCE_TYPE_MSG = {
  [RESOURCE_TYPE.CPU]: 'CPU',
  [RESOURCE_TYPE.GPU]: '加速卡',
};

// 服务列表-资源类型筛选值
export const SERVICE_ROURCE_TYPE_FILTER = [
  { text: 'CPU', value: SERVICE_ROURCE_TYPE_ENUM.CPU },
  { text: '加速卡', value: SERVICE_ROURCE_TYPE_ENUM.GPU },
];

// 服务列表
export const SERVICE_ROURCE_TYPE_TEXT_MAP = new Map([
  [SERVICE_ROURCE_TYPE_ENUM.CPU, 'CPU'],
  [SERVICE_ROURCE_TYPE_ENUM.GPU, 'GPU'],
]);

// 新建服务-镜像部署与模型部署-训练场景
export const IMAGE_SCENE_ENUM = {
  AIIP: '0', // 推理
  TRAINNING: '1', // 训练
};

export const IMAGE_SCENE_OPTIONS = [
  { label: '训练', value: IMAGE_SCENE_ENUM.TRAINNING },
  { label: '推理', value: IMAGE_SCENE_ENUM.AIIP },
];

export const MODEL_TYPE_ENUM = {
  COMPUTER_VISION: '0', // 计算机视觉
  NATURAL_LANGUAGE_PROCESSING: '1', // 自然语言处理
  INTELLIGENTVOICE: '2', // 语音
  BIG_MODEL: '3', // 大模型
  MULTIMODAL: '4', // 多模态
  OTHER: '5', // 其他
};

export const MODEL_TYPE_OPTIONS = [
  { value: MODEL_TYPE_ENUM.COMPUTER_VISION, label: '机器视觉' },
  { value: MODEL_TYPE_ENUM.NATURAL_LANGUAGE_PROCESSING, label: '自然语言处理' },
  { value: MODEL_TYPE_ENUM.INTELLIGENTVOICE, label: '语音' },
  { value: MODEL_TYPE_ENUM.BIG_MODEL, label: '大模型' },
  { value: MODEL_TYPE_ENUM.MULTIMODAL, label: '多模态' },
  { value: MODEL_TYPE_ENUM.OTHER, label: '其他' },
];

export const MODEL_TYPE_TEXT_MAP = new Map([
  [MODEL_TYPE_ENUM.COMPUTER_VISION, '机器视觉'],
  [MODEL_TYPE_ENUM.NATURAL_LANGUAGE_PROCESSING, '自然语言处理'],
  [MODEL_TYPE_ENUM.INTELLIGENTVOICE, '语音'],
  [MODEL_TYPE_ENUM.BIG_MODEL, '大模型'],
  [MODEL_TYPE_ENUM.MULTIMODAL, '多模态'],
  [MODEL_TYPE_ENUM.OTHER, '其他'],
]);

// 新建服务-控制台类型
export const CONSOLE_TYPE_ENUM = {
  SDTIN_AND_TTY: 0, // 交互&TTY终端(-i,-t)
  SDTIN: 1, // 交互终端(-i)
  TTY: 2, // TTY终端(-t)
  NONE: 3, // 无
};

export const CONSOLE_TYPE_TEXT_MAP = new Map([
  [CONSOLE_TYPE_ENUM.SDTIN_AND_TTY, '交互&TTY终端(-i,-t)'],
  [CONSOLE_TYPE_ENUM.SDTIN, '交互终端(-i)'],
  [CONSOLE_TYPE_ENUM.TTY, 'TTY终端(-t)'],
  [CONSOLE_TYPE_ENUM.NONE, '无'],
]);
// 新建服务-配置文件-配置类型枚举
export const typeOptions = [
  {
    // value: 'plain',
    value: 'configmap',
    label: '明文配置',
  },
  {
    value: 'secret',
    label: '密文配置',
  },
];
// 新建服务-配置文件-是否只读枚举
export const readOnlyOptions = [
  {
    value: 'true',
    label: '是',
  },
  {
    value: 'false',
    label: '否',
  },
];
// 选择的资源组卡类型
export const RESOURCE_TYPE_ENUM = {
  CPU: 'CPU', // cpu
  CARD: 'GPU', // 加速卡，记得以后会添加vgpu
};
export const SCENE_TYPE = {
  INFERENCE: 0,
  TRAIN: 1,
  INFER_AND_TRAIN: 2,
};

// 状态对应 中文文案
export const STATUS_TEXT_MAP = {
  UPDATING: '更新中',
  QUEUE: '排队中',
  STARTING: '启动中',
  STOPPING: '停止中',
  RUNNING: '运行中',
  FAIL: '失败',
  STOPPED: '已停止',
  SUCCESS: '成功',
  SNAPSHOT: '快照中',
  LOCKED: '已锁定',
  RETRYING: '重试中',
  PARTIALLYRUNNING: '部分运行中',
  FINISHED: '已完成',
  WAITING: '待评估',
};

// 数据解析状态
export const ANALYSIS_STATUS_TEXT = {
  UPDATING: '更新中',
  QUEUE: '排队中',
  STARTING: '启动中',
  STOPPING: '暂停中',
  RUNNING: '运行中',
  FAIL: '失败',
  STOPPED: '已暂停',
  SUCCESS: '成功',
  SNAPSHOT: '快照中',
  LOCKED: '已锁定',
  RETRYING: '重试中',
  PARTIALLYRUNNING: '部分运行中',
  FINISHED: '已完成',
};

//预训练状态对应文案
export const INSTATUS_TEXT_MAP = {
  WAITING: '排队中',
  STARTING: '启动中',
  STOPPING: '停止中',
  RUNNING: '运行中',
  FAILED: '失败',
  STOPPED: '已停止',
  SUCCEED: '成功',
  DELETED: '已删除',
};

// 服务声明跳转链接
export const serviceStatementUrl = 'common-helpcenter#/document/40?platformCode=JTRGZNPTFWXY';
