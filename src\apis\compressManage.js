import { requestWithProjectId } from '@/request/index';
const { POST, GET } = requestWithProjectId;

// 获取压缩任务列表
export const getMissionList = (data) => POST('/web/model/v1/compress/list', data);
// 任务删除
export const deleteMission = (data) => POST('/web/model/v1/compress/delete', data, { 'Content-Type': 'multipart/form-data' });
// 获取任务详情
export const getMissionDetail = (data) => POST('/web/model/v1/compress/detail', data, { 'Content-Type': 'multipart/form-data' });
// 停止任务
export const stopMission = (data) => POST('/web/model/v1/compress/stop', data, { 'Content-Type': 'multipart/form-data' });
// 运行任务
export const runMission = (data) => POST('/web/model/v1/compress/run', data, { 'Content-Type': 'multipart/form-data' });
// 发布任务
export const releaseMission = (data) => POST('/web/model/v1/compress/publish', data);
// 获取预制模型列表数据源
export const getPresetModelList = () => POST('/web/model/manage/preset/v1/list-all');
// 获取预制模型详情
export const getPresetModelDetail = (data) => GET('/web/model/manage/preset/v1/detail', data);
// 获取自定义模型列表数据源
export const getCustomModelList = (data) => POST('/web/model/v1/manage/list', data);
// 获取自定义模型对应的版本列表数据源
export const getVersionList = (data) => GET('/web/model/v1/manage/version/all', data);
// 新建压缩任务
export const createMission = (data) => POST('/web/model/v1/compress/create', data);
// 判断压缩任务名称是否存在
export const getMissionNameExist = (data) => POST('/web/model/v1/compress/name-exist', data, { 'Content-Type': 'multipart/form-data' });
// 生成已有模型新版本号
export const getNewVersion = (data) => POST('/web/model/v1/manage/version/num/generate', data);
// 判断模型名称是否存在
export const getModelNameExist = (data) => POST('/web/model/v1/manage/name-check', data, { 'Content-Type': 'multipart/form-data' });
// 获取模型网络数据源
export const getModelNetworkList = () => GET('/web/model/v1/manage/network/list');
// 新增模型网络数据
export const addModelNetwork = (data) => POST('/web/model/v1/manage/network/add', data, { 'Content-Type': 'multipart/form-data' });
// 删除模型网络数据
export const deleteModelNetwork = (data) => POST('/web/model/v1/manage/network/delete', data, { 'Content-Type': 'multipart/form-data' });
// 获取各种字典列表
export const getDictList = (data) => GET('/web/model/v1/manage/dict', data);
// 获取压缩策略数据源
export const getStrategyList = () => GET('/web/model/v1/compress/strategy-list');
// 获取压缩实时日志/web/model/v1/compress/log-ws-url
export const getCompressRealLog = (data) => GET('/web/model/v1/compress/log-ws-url', data);
// 获取压缩历史日志/web/model/v1/compress/his-log
export const getCompressHisLog = (data) => GET('/web/model/v1/compress/his-log', data);
// 获取版本详情
export const getVersionDetail = (data) => GET('/web/model/v1/manage/version/detail', data);
//  获取压缩的配置项
export const getCompressConfig = (data) => GET('/web/model/manage/preset/v1/config', data);
