// 任务类型枚举
export const IMAGE_SCENE_TYPE = {
  REASON: 0,
  TRAIN: 1,
  ALL: 2,
};

export const IMAGE_SCENE_MSG = {
  [IMAGE_SCENE_TYPE.REASON]: '推理',
  [IMAGE_SCENE_TYPE.TRAIN]: '训练',
  [IMAGE_SCENE_TYPE.ALL]: '训练推理',
};

export const ARCHITECTURE_TYPE = {
  X86: 0,
  ARM: 1,
};

export const ARCHITECTURE_TYPE_MSG = {
  [ARCHITECTURE_TYPE.X86]: 'X86',
  [ARCHITECTURE_TYPE.ARM]: 'ARM',
};

export const CHIP_TYPE = {
  CPU: 0,
  GPU: 1,
  NPU: 2,
  TPU: 3,
};

export const CHIP_TYPE_MSG = {
  [CHIP_TYPE.CPU]: 'CPU',
  [CHIP_TYPE.GPU]: 'GPU',
  [CHIP_TYPE.NPU]: 'NPU',
  [CHIP_TYPE.TPU]: 'TPU',
};

export const IDE_TYPE = {
  JUPYTER: 'Jupyter',
  VSCODE: 'VSCode',
  NONE: '-',
};

export const IDE_TYPE_MSG = {
  [IDE_TYPE.JUPYTER]: 'Jupyter',
  [IDE_TYPE.VSCODE]: 'VSCode',
  [IDE_TYPE.NONE]: '无',
};

export const IDE_TAG_COLOR_CLASS = {
  [IDE_TYPE.JUPYTER]: 'ant-tag-orange',
  [IDE_TYPE.VSCODE]: 'ant-tag-purple',
  [IDE_TYPE.NONE]: '',
};

export const CUSTOM_MIRROR_LIST_COLUMN = [
  {
    title: '镜像名称',
    dataIndex: 'imageGroupName',
    key: 'imageGroupName',
    ellipsis: true,
    width: 208,
  },
  {
    title: '适用场景',
    dataIndex: 'scene',
    key: 'scene',
    width: 110,
    filters: [
      {
        value: IMAGE_SCENE_TYPE.TRAIN,
        text: IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.TRAIN],
      },
      {
        value: IMAGE_SCENE_TYPE.REASON,
        text: IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.REASON],
      },
    ],
  },
  {
    title: '架构',
    dataIndex: 'architecture',
    key: 'architecture',
    width: 80,
    filters: [
      {
        value: ARCHITECTURE_TYPE.X86,
        text: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.X86],
      },
      {
        value: ARCHITECTURE_TYPE.ARM,
        text: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.ARM],
      },
    ],
    customRender: ({ text }) => {
      return text === 99 ? '--' : ARCHITECTURE_TYPE_MSG[text];
    },
  },
  {
    title: '芯片类型',
    dataIndex: 'chipType',
    key: 'chipType',
    width: 110,
    filters: [
      {
        value: CHIP_TYPE.GPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.GPU],
      },
      {
        value: CHIP_TYPE.NPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.NPU],
      },
      {
        value: CHIP_TYPE.CPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.CPU],
      },
    ],
    customRender: ({ text }) => {
      return text === 99 ? '--' : CHIP_TYPE_MSG[text];
    },
  },

  {
    title: '最新版本',
    dataIndex: 'latestVersion',
    key: 'latestVersion',
    width: 96,
  },
  {
    title: '版本数',
    dataIndex: 'versionNumber',
    key: 'versionNumber',
    sorter: true,
    width: 96,
  },
  {
    title: '创建人',
    dataIndex: 'createUserName',
    key: 'createUserName',
    width: 110,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    sorter: true,
    width: 160,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 190,
  },
];

export const PREPARE_MIRROR_LIST_COLUMN = [
  {
    title: '镜像名称',
    dataIndex: 'imageName',
    key: 'imageName',
    width: 240,
    fixed: 'left',
  },
  {
    title: '框架',
    dataIndex: 'framework',
    key: 'framework',
    width: 160,
  },
  {
    title: 'Python版本',
    dataIndex: 'pyVersion',
    key: 'pyVersion',
    width: 110,
  },
  {
    title: '适用场景',
    dataIndex: 'scene',
    key: 'scene',
    width: 110,
    filters: [
      {
        value: IMAGE_SCENE_TYPE.TRAIN,
        text: IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.TRAIN],
      },
      {
        value: IMAGE_SCENE_TYPE.REASON,
        text: IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.REASON],
      },
    ],
  },
  {
    title: '架构',
    dataIndex: 'architecture',
    key: 'architecture',
    ellipsis: true,
    width: 80,
    filters: [
      {
        value: ARCHITECTURE_TYPE.X86,
        text: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.X86],
      },
      {
        value: ARCHITECTURE_TYPE.ARM,
        text: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.ARM],
      },
    ],
    customRender: ({ text }) => {
      return text === 99 ? '--' : ARCHITECTURE_TYPE_MSG[text];
    },
  },
  {
    title: '芯片类型',
    dataIndex: 'chipType',
    key: 'chipType',
    width: 110,
    filters: [
      {
        value: CHIP_TYPE.GPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.GPU],
      },
      {
        value: CHIP_TYPE.NPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.NPU],
      },
      {
        value: CHIP_TYPE.CPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.CPU],
      },
    ],
    customRender: ({ text }) => {
      return text === 99 ? '--' : CHIP_TYPE_MSG[text];
    },
  },
  {
    title: 'IDE支持',
    dataIndex: 'ideType',
    key: 'ideType',
    width: 110,
    filters: [
      {
        value: IDE_TYPE.JUPYTER,
        text: IDE_TYPE_MSG[IDE_TYPE.JUPYTER],
      },
      {
        value: IDE_TYPE.VSCODE,
        text: IDE_TYPE_MSG[IDE_TYPE.VSCODE],
      },
      {
        value: IDE_TYPE.NONE,
        text: IDE_TYPE_MSG[IDE_TYPE.NONE],
      },
    ],
  },
  {
    title: '镜像地址',
    dataIndex: 'address',
    key: 'address',
    width: 220,
  },
  {
    title: '镜像标签',
    dataIndex: 'tags',
    key: 'tags',
    width: 100,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    sorter: true,
    width: 190,
  },
  // {
  //   title: '镜像描述',
  //   dataIndex: 'description',
  //   key: 'description',
  //   width: 2,
  // },
];

// 运管侧，预置资产-预置镜像列表
export const MANAGE_PRESET_IMAGE_TABLE_COLUMN = [
  {
    title: '镜像名称',
    dataIndex: 'imageName',
    key: 'imageName',
    ellipsis: true,
  },
  {
    title: '镜像状态',
    dataIndex: 'status',
    key: 'status',
    filters: [
      {
        value: 0,
        text: '上架',
      },
      {
        value: 1,
        text: '下架',
      },
    ],
  },
  {
    title: '适用场景',
    dataIndex: 'scene',
    key: 'scene',
    filters: [
      {
        value: IMAGE_SCENE_TYPE.TRAIN,
        text: IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.TRAIN],
      },
      {
        value: IMAGE_SCENE_TYPE.REASON,
        text: IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.REASON],
      },
    ],
  },
  {
    title: '架构',
    dataIndex: 'architecture',
    key: 'architecture',
    filters: [
      {
        value: ARCHITECTURE_TYPE.X86,
        text: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.X86],
      },
      {
        value: ARCHITECTURE_TYPE.ARM,
        text: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.ARM],
      },
    ],
    customRender: ({ text }) => {
      return text === 99 ? '--' : ARCHITECTURE_TYPE_MSG[text];
    },
  },
  {
    title: '芯片类型',
    dataIndex: 'chipType',
    key: 'chipType',
    filters: [
      {
        value: CHIP_TYPE.GPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.GPU],
      },
      {
        value: CHIP_TYPE.NPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.NPU],
      },
      {
        value: CHIP_TYPE.CPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.CPU],
      },
    ],
    customRender: ({ text }) => {
      return text === 99 ? '--' : CHIP_TYPE_MSG[text];
    },
  },
  {
    title: '镜像性质',
    dataIndex: 'imageType',
    key: 'imageType',
    filters: [
      {
        value: 0,
        text: '平台预置镜像',
      },
      {
        value: 1,
        text: '工具类镜像',
      },
    ],
    customRender: ({ text }) => {
      return text === 0 ? '平台预置镜像' : '工具类镜像';
    },
  },
  {
    title: '上/下架时间',
    dataIndex: 'shelveTime',
    key: 'shelveTime',
    width: 200,
    sorter: true,
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    width: 200,
    sorter: true,
  },
];
export const SHARE_MIRROR_LIST_COLUMN = [
  {
    title: '镜像文件名称',
    dataIndex: 'shareImageGroupName',
    key: 'shareImageGroupName',
    ellipsis: true,
    width: 148,
  },
  {
    title: '适用场景',
    dataIndex: 'scene',
    key: 'scene',
    width: 110,
    filters: [
      {
        value: IMAGE_SCENE_TYPE.TRAIN,
        text: IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.TRAIN],
      },
      {
        value: IMAGE_SCENE_TYPE.REASON,
        text: IMAGE_SCENE_MSG[IMAGE_SCENE_TYPE.REASON],
      },
    ],
  },
  {
    title: '架构',
    dataIndex: 'architecture',
    key: 'architecture',
    width: 80,
    filters: [
      {
        value: ARCHITECTURE_TYPE.X86,
        text: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.X86],
      },
      {
        value: ARCHITECTURE_TYPE.ARM,
        text: ARCHITECTURE_TYPE_MSG[ARCHITECTURE_TYPE.ARM],
      },
    ],
    customRender: ({ text }) => {
      return text === 99 ? '--' : ARCHITECTURE_TYPE_MSG[text];
    },
  },
  {
    title: '芯片类型',
    dataIndex: 'chipType',
    key: 'chipType',
    width: 110,
    filters: [
      {
        value: CHIP_TYPE.GPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.GPU],
      },
      {
        value: CHIP_TYPE.NPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.NPU],
      },
      {
        value: CHIP_TYPE.CPU,
        text: CHIP_TYPE_MSG[CHIP_TYPE.CPU],
      },
    ],
    customRender: ({ text }) => {
      return text === 99 ? '--' : CHIP_TYPE_MSG[text];
    },
  },
  {
    title: '镜像文件ID',
    dataIndex: 'imageSha',
    key: 'imageSha',
    ellipsis: true,
    width: 196,
  },
  {
    title: '分享人',
    dataIndex: 'shareBy',
    key: 'shareBy',
    width: 110,
  },
  {
    title: '分享时间',
    dataIndex: 'shareTime',
    key: 'shareTime',
    sorter: true,
    width: 160,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 100,
  },
];

export const SYNC_METHOD = {
  EXIST: 1, // 同步至已有镜像新版本
  CREATE: 2, // 同步为新镜像
};

export const IMAGE_STATUS = {
  DELETED: 1, // 已删除
  UNDELETED: 0, // 未删除
};

export const IMAGE_ORIGIN = {
  OBJECT_IMPORT: 0, // 对象存储导入
  DEV_SAVE: 1, // 开发环境保存
};

// 导入镜像的状态
export const IMPORT_IMAGE_STATUS = {
  COMPLETED: { code: 0, tagColor: 'green', message: '完成' },

  IMPORTING: { code: 1, tagColor: 'blue', message: '导入中' },
  NOT_DELETION_AND_NOT_SUCCESS: { code: 99, tagColor: 'blue', message: '非删除、非成功状态的镜像文件' },

  // * 错误态
  FAILED : { code: -1, tagColor: 'red', message: '未知异常' },
  CHECK_PARAM_ERROR: { code: -2, tagColor: 'red', message: '校验参数失败' },
  PAUSE_CONTAINER_ERROR: { code: -3, tagColor: 'red', message: '暂停容器失败' },
  COMMIT_CONTAINER_ERROR: { code: -4, tagColor: 'red', message: '保存容器失败' },
  RESTART_CONTAINER_ERROR: { code: -5, tagColor: 'red', message: '重启容器失败' },
  HARBOR_LOGIN_ERROR: { code: -6, tagColor: 'red', message: 'HARBOR登录失败' },
  IMAGE_PULL_ERROR: { code: -7, tagColor: 'red', message: '拉取镜像失败' },
  IMAGE_PUSH_ERROR: { code: -8, tagColor: 'red', message: '推送镜像失败' },
  SAVE_OR_IMPORT_IMAGE_TIMEOUT: { code: -9, tagColor: 'red', message: '导入镜像超时' },
  LOGIN_OSS_ERROR: { code: -11, tagColor: 'red', message: 'OSS登录失败' },
  COPY_FILE_ERROR: { code: -12, tagColor: 'red', message: 'OSS拉取文件失败' },
  IMAGE_LOAD_ERROR: { code: -13, tagColor: 'red', message: '载入镜像失败' },
};

// 获取详细的镜像导入状态信息
export const getImportMsgByBackendStatus = (source, code) => {
  for (const i in IMPORT_IMAGE_STATUS) {
    const temp = IMPORT_IMAGE_STATUS[i];
    if (temp.code === code) {
      if (code === IMPORT_IMAGE_STATUS.SAVE_OR_IMPORT_IMAGE_TIMEOUT.code && source === IMAGE_ORIGIN.DEV_SAVE) {
        temp.message = '保存镜像超时';
      }
      return temp;
    }
  }
};


// 导出镜像的状态
export const EXPORT_IMAGE_STATUS = {
  SUCCESS: { code: 0, tagColor: 'green', message: '成功' },
  FAILED: { code: -1, tagColor: 'red', message: '失败' },

  // * 中间态：
  EXPORTING: { code: 1, tagColor: 'blue', message: '进行中' },
  CHECK_PARAM: { code: 2, tagColor: 'blue', message: '校验参数中' },
  HARBOR_LOGIN: { code: 3, tagColor: 'blue', message: '镜像仓库登录中' },
  IMAGE_PULLING: { code: 4, tagColor: 'blue', message: '镜像拉取中' },
  IMAGE_SAVING: { code: 5, tagColor: 'blue', message: '镜像保存中' },
  MINIO_LOGIN: { code: 6, tagColor: 'blue', message: '对象存储登录' },
  IMAGE_COPYING: { code: 7, tagColor: 'blue', message: '镜像包到对象存储复制中' },

  // * 错误态
  CHECK_PARAM_ERROR: { code: -2, tagColor: 'red', message: '校验参数失败' },
  HARBOR_LOGIN_ERROR: { code: -3, tagColor: 'red', message: '镜像仓库登录失败' },
  IMAGE_PULLING_ERROR: { code: -4, tagColor: 'red', message: '拉取镜像失败' },
  IMAGE_SAVING_ERROR: { code: -5, tagColor: 'red', message: '保存镜像失败' },
  MINIO_LOGIN_ERROR: { code: -6, tagColor: 'red', message: 'OSS登录失败' },
  IMAGE_COPYING_ERROR: { code: -7, tagColor: 'red', message: 'OSS复制失败' },
  EXPORT_IMAGE_TIMEOUT: { code: -9, tagColor: 'red', message: '导出镜像超时' },
};

/**
 * 需要使用后端返回的错误文案的错误码集合
 * @returns {Array} 错误码
 */
export const imageErrorUseBackMsg = (() => {
  const tagsCode = [121000014];
  return [...tagsCode];
})();

export const imageProjectNotExistCode = 12200029;
