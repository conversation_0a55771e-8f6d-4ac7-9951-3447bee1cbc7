import { toGB } from '@/utils';

export const STORAGE_DIR_TABS_MAP = {
  PROJECT: '1',
  EXTERNAL: '2',
};
export const STORAGE_TYPE_MAP = {
  GENERAL_STOR: 'GENERAL_STOR', // 普通文件存储
  HS_STOR: 'HS_STOR', // 高性能文件存储
  OBJECT_STOR: 'OBJECT_STOR', // 对象存储
};
// 操作入口 1项目空间存储目录 2挂载外部存储目录
export const OPERATION_ENTRANCE = {
  PROJECT: 1,
  EXTERNAL: 2,
};
export const MOUNTEXTERNALDIR_HELP = {
  NFS: '请按照ip:/path格式填写，数字或字母结尾，仅支持冒号:、中划线-、下划线_、小数点.、斜杠/五种字符',
  POSIX: '请按照/path格式填写，数字或字母结尾，仅支持冒号:、中划线-、下划线_、小数点.、斜杠/五种字符',
};
/* 
  "apv": {
    "quota": ["view"] //配额扩容申请工单权限
  },
  "mgt": {
      "quota": ["view", "edit"], //配额管理权限
      "resgroup": ["view", "edit"] //资源组管理权限
  },
*/
export const MGT_STODIR_VIEW_AUTH = { type: 'mgt', key: 'stodir', value: 'view' };
export const MGT_STODIR_EDIT_AUTH = { type: 'mgt', key: 'stodir', value: 'edit' };
export const INSIDE_DIR_DEFAULT_PREFIX = '/root/work/externalstorage'; // 项目内部目录 默认前缀
export const INSIDE_DIR_FORMITEM_TEXT = '以/开头，数字或字母结尾，仅支持中划线-、下划线_、小数点.、斜杠/四种字符'; // 挂载至项目内部目录 表单help文案
export const FORMITEM_TEXT = '请输入1000个字符以内';

export const getRecordStorageText = (key, record, needUnit = false, fixedNum = 1) => {
  const subInfo = record?.subInfoList?.find((x) => x.storageType === key);
  if (!subInfo) {
    return `--`;
  }
  const toGbObj = { unitDisplayFull: true, displayFullField: true, unitIfZero: false };
  return `${toGB(subInfo.used, fixedNum, toGbObj)} / ${toGB(subInfo.quota, fixedNum, toGbObj)}${needUnit ? '（已用/总共）' : ''}`;
};
export const getRecordIdOrBucket = (key, record) => {
  const subInfo = record?.subInfoList?.find((x) => x.storageType === key);
  if (!subInfo) {
    return `--`;
  }
  // 如果是对象存储（OBJECT_STOR），那么有bucketName，没有storagePath，如果是其他存储，那么是有storagePath，没有bucketName
  if (key === 'OBJECT_STOR') {
    return subInfo.bucketName;
  }
  return subInfo.storagePath;
};
