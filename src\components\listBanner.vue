<template>
  <div v-if="!closed" class="list-banner" :class="collapse ? 'collapse' : ''" :style="bannerStyle">
    <div class="list-banner-content">
      <h2>{{ title }}</h2>
      <slot v-if="collapse" name="longText"></slot>
      <p v-if="description && collapse">{{ description }}</p>
      <div v-if="collapse" class="content-group">
        <slot v-if="$slots.default"></slot>
        <template v-else>
          <div v-for="(x, i) in list" :key="i" class="group-item" :style="{ width: 100 / list.length + '%', ...x.style }">
            <h3>{{ x.title }}</h3>
            <renderDom :v-node="x.content" />
            <div v-if="x.buttonGroup && x.buttonGroup.length" class="button-group">
              <span v-for="(buttonItem, index) in x.buttonGroup" :key="index" class="button" @click="buttonItem.onClick"
                >{{ buttonItem.text }}
                <jt-icon type="iconjiantouyou" class="iconfont" />
              </span>
            </div>
          </div>
        </template>
      </div>

      <a-space :size="8" class="operation-btns">
        <jt-icon :type="collapse ? 'iconshouqi' : 'iconzhankai'" class="iconfont" @click="handleToggle" />
        <jt-icon type="iconguanbi" class="iconfont" @click="handleClose" />
      </a-space>
    </div>

    <img v-if="collapse" :src="bannerImg" class="banner-img" :style="imgStyle" alt="" />
    <div class="banner-bg" :class="collapse ? 'collapse' : ''"></div>
  </div>
</template>

<script>
export default {
  components: {
    renderDom: {
      props: {
        vNode: [Array, String, Object, Number, Function],
      },
      render(h) {
        if (typeof this.vNode === 'object') {
          return this.vNode;
        } else if (typeof this.vNode === 'function') {
          return this.vNode();
        }
        return h('div', this.vNode);
      },
    },
  },
  props: {
    storageKey: {
      type: String,
      default: 'listBanner',
      require: true,
    },
    title: {
      type: String,
      default: '欢迎使用任务建模！',
    },
    description: {
      type: String,
      default: '',
    },
    bannerImg: {
      type: String,
      default: require('@/assets/images/list-banner/instance-new.png'),
    },
    bannerStyle: {
      type: Object,
      default: () => {
        return {};
      },
    },
    imgStyle: {
      type: Object,
      default: () => {
        return {};
      },
    },
    list: {
      type: [Array, Object],
      default: () => {
        return [];
      },
    },
  },
  data() {
    return {
      collapse: true,
      closed: false,
      isCalcHeight: false,
    };
  },
  computed: {
    // 防止storageKey重复
    computedStorageKey() {
      return this.$props.storageKey + `（${this.$route.fullPath}）`;
    },
  },
  watch: {
    collapse() {
      this.$nextTick(() => {
        this.calcGroupItemMinHeight();
      });
    },
  },
  created() {
    const introState = sessionStorage.getItem(this.computedStorageKey);
    if (introState === 'closed') {
      this.closed = true;
    }
    if (introState === 'collapse') {
      this.closed = false;
      this.collapse = true;
    }
    if (introState === '') {
      this.collapse = false;
    }
  },
  mounted() {
    if (this.list) {
      this.$nextTick(() => {
        this.calcGroupItemMinHeight();
      });
    }
    window.onresize = () => {
      this.$nextTick(() => {
        this.calcGroupItemMinHeight();
      });
    };
  },
  methods: {
    calcGroupItemMinHeight() {
      if (!this.list.some((x) => x.buttonGroup) && !document.querySelector('.button-group')) {
        return;
      }
      let groupItem = document.querySelector('.group-item');
      const itemHeight = groupItem.offsetHeight;

      let groupItemText = document.querySelector('.list-banner-content').querySelectorAll('p');
      const arrHeight = [];
      groupItemText.forEach((item) => {
        arrHeight.push(item.offsetHeight);
      });
      const max = Math.max(...arrHeight);

      const diff = itemHeight - max;

      if (diff < 30) {
        const fixHeight = itemHeight + 14;
        document.querySelectorAll('.group-item').forEach((groupItem) => {
          groupItem.style.minHeight = fixHeight + 'px';
        });
      } else if (diff > 48) {
        document.querySelectorAll('.group-item').forEach((groupItem) => {
          groupItem.style.minHeight = itemHeight - 20 + 'px';
        });
      }
    },
    // calcGroupItemMinHeight() {
    //   if (!this.list.some((x) => x.buttonGroup) && !document.querySelector('.button-group')) {
    //     return;
    //   }
    //   let groupItem = document.querySelector('.group-item');
    //   if (groupItem) {
    //     const itemHeight = groupItem.offsetHeight;
    //     const minHeight = itemHeight + 12; // 数值为根据设计稿比对得出的结果
    //     document.querySelectorAll('.group-item').forEach((groupItem) => {
    //       groupItem.style.minHeight = minHeight + 'px';
    //     });
    //   }
    // },
    handleToggle() {
      this.collapse = !this.collapse;
      sessionStorage.setItem(this.computedStorageKey, this.collapse ? 'collapse' : '');
    },
    handleClose() {
      this.closed = true;
      sessionStorage.setItem(this.computedStorageKey, 'closed');
    },
  },
};
</script>

<style lang="less" scoped>
.list-banner {
  margin: 22px 20px 0px;
  padding: 20px;
  position: relative;
  background-color: #fff;
  flex-shrink: 0;
  border-radius: 4px;
  box-shadow: @jt-box-shadow;
  &.collapse {
    padding: 20px 20px 25px;
    min-height: 161px;
  }
}
.list-banner-content {
  z-index: 1;
  position: relative;
  > p {
    font-size: @jt-font-size-base;
    color: @jt-text-color-primary-opacity07;
    line-height: 22px;
    width: 65%;
    margin-top: 12px;
  }
}
.banner-bg {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  opacity: 0.28;
  &.collapse {
    // background-image: linear-gradient(180deg, rgb(83, 229, 255) 0%, rgba(60, 183, 255, 0.6) 40%, rgba(255, 255, 255, 0) 100%);
  }
}
h2 {
  font-size: @jt-font-size-lger;
  font-weight: @jt-font-weight-medium;
  color: #121f2c;
  margin-bottom: 0;
}
.banner-img {
  height: 169px;
  position: absolute;
  right: 0;
  top: -20px;
}
.operation-btns {
  position: absolute;
  top: 0;
  right: 0;
  span {
    background-color: #fff;
    display: inline-block;
    height: 24px;
    line-height: 26px;
    width: 24px;
    border-radius: 2px;
    border: 1px solid rgba(0, 20, 26, 0.15);
    text-align: center;
    cursor: pointer;
    color: rgba(0, 20, 26, 0.45);
    transition: 0.3s all;
    &:hover {
      color: @jt-primary-color;
      border-color: @jt-primary-color;
    }
  }
}
.content-group {
  display: flex;
  justify-content: space-between;
  max-width: ~'calc(95% - 300px)';
  font-size: @jt-font-size-sm;
  margin-top: 16px;
  .group-item,
  :slotted(.group-item) {
    padding: 0 26px 0 14px;
    position: relative;
    background-size: 102px 40px;
    background-position: calc(100% - 20px) top;
    background-repeat: no-repeat;
    width: 100%;
    border-left: 2px solid #2bb4d6;
    &:nth-of-type(1) {
      background-image: url('~@/assets/images/list-banner/banner-num-1.png');
    }
    &:nth-of-type(2) {
      background-image: url('~@/assets/images/list-banner/banner-num-2.png');
    }
    &:nth-of-type(3) {
      background-image: url('~@/assets/images/list-banner/banner-num-3.png');
    }
  }
  h3,
  p,
  :slotted(h3),
  :slotted(p) {
    margin-bottom: 0;
    line-height: 20px;
  }
  h3,
  :slotted(h3) {
    font-weight: 600;
    font-size: 14px;
    color: #091b2d;
    line-height: 20px;
  }
  p,
  :slotted(p) {
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 20, 26, 0.7);
    margin: 4px 0;
    max-width: 95%;
  }
}
.button-group,
:slotted(.button-group) {
  position: absolute;
  bottom: 0;
  .button {
    cursor: pointer;
    color: @jt-primary-color;
    & ~ .button {
      margin-left: 16px;
    }
    &:hover {
      color: @jt-link-button-hover-color;
    }
  }
}
</style>
