// 预制模型文件是否支持开源
export const MODEL_FILE_SUPPORT = {
  OPEN_SOURCE: 1,
  NOT_OPEN_SOURCE: 0,
};
// 预制模型文件来源
export const MODEL_FILE_SOURCE = {
  FILE_STORAGE: '0',
  OBJECT_STORAGE: '1',
};
// 数据集分类
export const DATASET_CLASSIFY_MAP = {
  0: '文本',
  1: '图像',
  2: '音频',
  3: '视频',
  4: '多模态',
  5: '其他',
};
export const DATASET_TYPE_MAP = {
  0: '问答对',
  1: '纯文本',
  2: '图文问答对',
  5: '图文对',
};

// 镜像状态
export const MIRROR_STATUS = {
  REMOVED: 1, // 下架状态
  LAUNCHED: 0, // 上架状态
};

// 镜像类型
export const IMAGE_TYPE = {
  PRESET_IMAGE: 0,
  UTILS_IMAGE: 1,
};

// 镜像类型名字
export const IMAGE_TYPE_TXT = {
  [IMAGE_TYPE.PRESET_IMAGE]: '平台预置镜像',
  [IMAGE_TYPE.UTILS_IMAGE]: '工具类镜像',
};

// 提示词状态
export const PROMPT_STATUS = {
  REMOVED: 0, // 下架状态
  LAUNCHED: 1, // 上架状态
};

export const PRESET_ASSETS_TABS = {
  MODEL: 'model',
  MIRROR: 'mirror',
  PROMPT: 'prompt',
};

export const PRESET_ASSETS_NAME = {
  [PRESET_ASSETS_TABS.MODEL]: '模型',
  [PRESET_ASSETS_TABS.MIRROR]: '镜像',
  [PRESET_ASSETS_TABS.PROMPT]: '提示词',
};

export const IMAGE_SCENE_TYPE = {
  TRAIN: 1,
  INFERENCE: 0,
  ALL: 2,
};

export const ARCHITECTURE_TXT = {
  0: 'X86',
  1: 'ARM',
};

export const CHIPTYPE_TXT = {
  0: 'CPU',
  1: 'GPU',
  2: 'NPU',
};

export const FRAMEWORK_TYPE_TXT = {
  0: 'TGI接口',
  1: 'vLLM接口',
  2: 'OpenAI接口',
};

export const PROTOCOLTYPE_TXT = {
  1: 'TCP',
  2: 'UDP',
};

export const PRESET_ASSETS_VIEW_AUTH = { type: 'mgt', key: 'preset', value: 'view' };
export const PRESET_ASSETS_EDIT_AUTH = { type: 'mgt', key: 'preset', value: 'edit' };

export const STREAMOUT_HELP = {
  STACK: `该模型流式输出的各条信息均包含上条信息的内容, 如：第一条: '你好', 第二条：'你好,有什么', 第三条：'你好, 有什么可以帮助你的吗'`,
  INCREMENT: `该模型流式输出的各条信息不包含上条信息的内容，如：第一条：'你好，'，第二条：'有什么'，第三条：'可以帮助你的吗'`,
};

export const STREAMOUT_TYPE_MAP = {
  0: '叠加式',
  1: '增量式',
};

export const IMAGE_SOURCE_MAP = {
  PROJECT_SPACE: 0,
  PRESET_IMAGE: 1,
};

export const IMAGE_SOURCE_TEXT = {
  [IMAGE_SOURCE_MAP.PROJECT_SPACE]: '用户项目空间',
  [IMAGE_SOURCE_MAP.PRESET_IMAGE]: '预置镜像仓库',
};
