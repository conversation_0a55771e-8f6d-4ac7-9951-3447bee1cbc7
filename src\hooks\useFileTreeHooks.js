import { ref, onMounted } from 'vue';
import { requestWithProjectId } from '@/request/index';
const { GET } = requestWithProjectId;
import { serviceApi } from '@/apis';
import { getStorageType } from '@/utils/service';
import { VOLUMES_STORAGE_TYPE_ENUM } from '@/constants/service';
import cloneDeep from 'lodash/cloneDeep';

export default function useFileTreeHooks() {
  let treefileUrl = ''; // 树形文件获取的地址，针对普通存储与高性能存储
  // treeselect生成一个随机数
  const treeData = ref([]);
  const treeInitData = ref([
    {
      id: `/root`,
      pId: 'root',
      key: `/root`,
      value: `/root`,
      title: `root`,
      path: `/root`,
      name: 'root/',
      isLeaf: false,
      disabled: true,
    },
    {
      id: `/root/work`,
      pId: `/root`,
      key: `/root/work`,
      value: `/root/work`,
      title: `work`,
      path: `/root/work`,
      name: 'root/work/',
      isLeaf: false,
      disabled: true,
    },
  ]);
  const treeDataF = ref([]); // 普通存储与高性能存储数据
  const treeDataE = ref([]); // 外部扩展存储数据
  const externalOriginData = ref([]); // 请求的外部扩展最原始的数据
  const initTreeData = async () => {
    try {
      const res = await serviceApi.getTreeFileUrl();
      if (res.code === 0 && res.data) {
        treefileUrl = res.data;
        const initTreeResult = await GET(treefileUrl, { dir: '', storageType: '' });
        if (initTreeResult?.code === 0) {
          const data = initTreeResult?.data?.filter((item) => item?.objectName !== 'externalstorage/'); // 过滤掉externalstorage/的目录
          let newData = cloneDeep(data);
          newData = newData?.map((item) => {
            if (item.objectName === '/filestorage' || item.objectName === 'filestorage') {
              return {
                ...item,
                objectName: 'filestorage/',
              };
            }
            return {
              ...item,
              objectName: item?.objectName?.replace(/^\//, ''), // 将所有objectName的前面的/去掉
            };
          });
          treeDataF.value = formatTreeData('/root/work', newData || [], '/root/work');
          treeData.value = [...treeInitData.value, ...treeDataF.value, ...treeDataE.value];
        } else {
          treeData.value = [...treeInitData.value, ...treeDataE.value];
        }
      } else {
        treeData.value = [...treeInitData.value, ...treeDataE.value];
      }
    } catch (e) {
      treeData.value = [...treeInitData.value, ...treeDataE.value];
      throw new Error(e);
    }
  };
  // 截取2个斜杠之间的内容
  const getContentBetweenSlashes = (url) => {
    const match = url.match(/\/([^\/]*)\/[^\/]*/);
    return match ? match[1] : null;
  };
  // 生成扩展存储第2层级
  const formatExternalTreeData = (treeNode) => {
    let arr = [];
    externalOriginData.value?.forEach((item) => {
      const nametemp = treeNode.name?.replace(/^\/root\/work\//, ''); // 去掉前面的/root/work/
      if (item.startsWith(nametemp)) {
        const str = item.replace(`${nametemp}`, '');
        if (str) {
          if (str.split('/').length - 1 == 1) {
            // 如果只有一个/，直接截取/后面的内容
            const index = str.lastIndexOf('/');
            const s = str.substring(index + 1, str.length);
            if (s) {
              const obj = {
                objectName: s,
                dir: true,
              };
              arr.push(obj);
            }
          } else if (str.split('/').length - 1 > 1) {
            // 有多个/,则截取2个第一个斜杠与第2个斜杠之间的内容
            if (getContentBetweenSlashes(str)) {
              const obj = {
                objectName: getContentBetweenSlashes(str),
                dir: true,
              };
              arr.push(obj);
            }
          }
        }
      }
    });
    // 去重
    if (arr.length) {
      arr = Array.from(new Set(arr?.map((item) => JSON.stringify(item))))?.map((item) => JSON.parse(item));
    }
    return arr;
  };
  const getRandom = () => {
    return Math.random().toString(36).substring(2, 6);
  };
  // 获取外部存储数据
  const initExternalTreeData = async () => {
    try {
      const res = await serviceApi.getTreeExtendFileUrl();
      if (res.code === 0) {
        const temp = cloneDeep(res.data);
        externalOriginData.value = temp;
        let fakeTreeData = [];
        if (res?.data.length) {
          fakeTreeData = [
            {
              objectName: 'externalstorage/',
              dir: true,
            },
          ];
        }
        treeDataE.value = formatTreeData('/root/work', fakeTreeData || [], '/root/work');
        treeData.value = [...treeInitData.value, ...treeDataF.value, ...treeDataE.value];
      } else {
        treeData.value = [...treeInitData.value, ...treeDataF.value];
      }
    } catch (e) {
      treeData.value = [...treeInitData.value, ...treeDataF.value];
      throw new Error(e);
    }
  };
  // 去掉尾部的/
  const trimTrailingSlash = (str) => {
    if (str?.endsWith('/')) {
      return str.slice(0, -1);
    }
    return str;
  };
  // treeselect判断是否禁用
  const getDisabledStatus = (item, parentId) => {
    const externalStatus = item.dir && parentId === '/root/work' && item.objectName === 'externalstorage/';
    return externalStatus || (item.dir && parentId === '/root/work' && (item.objectName === 'filestorage/' || item.objectName === 'filestorage' || item.objectName === '/filestorage/' || item.objectName === '/filestorage'));
  };
  // 格式化树形数据
  const formatTreeData = (parentId, tree, parentPath = '') => {
    if (!tree.length) return [];
    const arr = [];
    for (const item of tree) {
      // 只显示文件夹
      if (item.dir) {
        const random = getRandom();
        const name = parentPath ? trimTrailingSlash(`${parentPath}/${item.objectName}`) : trimTrailingSlash(`/${item.objectName}`);
        const node = {
          id: `${name}-${random}`,
          pId: parentId,
          key: `${name}-${random}`,
          value: `${name}-${random}`,
          title: trimTrailingSlash(item.objectName),
          path: name,
          name,
          isLeaf: false,
          disabled: getDisabledStatus(item, parentId),
        };
        arr.push(node);
      }
    }
    return arr;
  };
  // 加载树形数据
  const onLoadData = (treeNode) => {
    console.log('🚀 --------- onLoadData --------- treeNode:', treeNode);
    // 防止a-tree-select组件销毁的重复加载问题
    const exitsChildren = treeData.value.find((item) => treeNode.id === item.pId);
    return new Promise((resolve) => {
      if (exitsChildren) {
        resolve(true);
      } else {
        if (treeNode.path === '/root/work') {
          resolve(true);
        } else {
          const params = {
            dir: trimTrailingSlash(treeNode.path).replace(/^\/root\/work\//, ''), // 去掉前面的/root/work/
            storageType: '',
          };
          const storageType = getStorageType(treeNode.path);
          if (storageType === VOLUMES_STORAGE_TYPE_ENUM.EXTERNAL_STOR) {
            // 如果是外部存储的话，不需要请求数据
            const fakeData = formatExternalTreeData(treeNode);
            treeData.value = treeData.value.concat(formatTreeData(treeNode.id, fakeData, treeNode.name));
            resolve(true);
          } else {
            GET(treefileUrl, params).then((res) => {
              if (res.code === 0) {
                treeData.value = treeData.value.concat(formatTreeData(treeNode.id, res.data, treeNode.name));
              }
              resolve(true);
            });
          }
        }
      }
    });
  };
  onMounted(() => {
    initTreeData();
    initExternalTreeData();
  });
  return {
    treeData,
    onLoadData,
  };
}
