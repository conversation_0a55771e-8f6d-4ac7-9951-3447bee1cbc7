import { getFormatTime } from '@/utils/index';

// 资源组大类（公共资源组、专属资源组）
export const RESOURCE_GROUP_TYPE = {
  PUBLIC: 'public', // 公共资源组
  PERSONAL: 'personal', // 专属资源组
};

// 评估方法
export const RULE_TYPE = {
  AUTOMATIC: '0', // 自动规则打分
  REFEREE: '1', // 裁判员打分
};

export const RESOURCE_ATTRIBUTES = {
  public: '公共',
  private: '专属',
};

export const MODEL_TYPE = {
  0: '计算机视觉',
  1: '自然语言处理',
  2: '语音',
  3: '大模型',
  4: '多模态',
  5: '其他',
  10: '图文对-文生图',
};

export const STATUS = Object.create({
  onSTART: 1, // 启动中
  WAITING: 2, // 排队中
  RUNNING: 3, // 运行中
  SUCCESS: 4, // 成功
  FAIL: 5, // 失败
  onSTOP: 6, // 停止中
  STOPED: 7, // 已停止
});
export const STATUS_COLOR = new Map([
  [STATUS.onSTART, 'orange'],
  [STATUS.WAITING, 'quete-yellow-jt'],
  [STATUS.RUNNING, 'green'],
  [STATUS.SUCCESS, 'mirror-blue-jt'],
  [STATUS.FAIL, 'red'],
  [STATUS.onSTOP, 'orange'],
  [STATUS.STOPED, 'default'],
]);
/**
 * 0 报告 1 停止 2 复制 3 删除
 */
export const STATUS_OP_SWITCH = new Map([
  [
    STATUS.onSTART,
    [
      { visiable: false, disabled: true },
      { visiable: true, disabled: false },
      { visiable: true, disabled: true },
      { visiable: true, disabled: true },
    ],
  ],
  [
    STATUS.WAITING,
    [
      { visiable: false, disabled: true },
      { visiable: true, disabled: false },
      { visiable: true, disabled: true },
      { visiable: true, disabled: true },
    ],
  ],
  [
    STATUS.RUNNING,
    [
      { visiable: false, disabled: true },
      { visiable: true, disabled: false },
      { visiable: true, disabled: false },
      { visiable: true, disabled: true },
    ],
  ],
  [
    STATUS.SUCCESS,
    [
      { visiable: true, disabled: false },
      { visiable: false, disabled: true },
      { visiable: true, disabled: false },
      { visiable: true, disabled: false },
    ],
  ],
  [
    STATUS.FAIL,
    [
      { visiable: false, disabled: true },
      { visiable: true, disabled: true },
      { visiable: true, disabled: false },
      { visiable: true, disabled: false },
    ],
  ],
  [
    STATUS.onSTOP,
    [
      { visiable: false, disabled: true },
      { visiable: true, disabled: true },
      { visiable: true, disabled: false },
      { visiable: true, disabled: true },
    ],
  ],
  [
    STATUS.STOPED,
    [
      { visiable: false, disabled: true },
      { visiable: true, disabled: true },
      { visiable: true, disabled: false },
      { visiable: true, disabled: false },
    ],
  ],
]);
export const STATUS_CH = new Map([
  [STATUS.onSTART, '启动中'],
  [STATUS.WAITING, '排队中'],
  [STATUS.RUNNING, '运行中'],
  [STATUS.SUCCESS, '成功'],
  [STATUS.FAIL, '失败'],
  [STATUS.onSTOP, '停止中'],
  [STATUS.STOPED, '已停止'],
]);

export const STATUS_FILTER = Array.from(STATUS_CH.keys()).map((key) => {
  const text = STATUS_CH.get(key);

  return {
    text: text,
    value: key,
  };
});
export const getResourceInfoWord = (value) => {
  let textArray = [];
  if (+value.gpu) {
    textArray.push(`${value.gpu}加速卡`);
  }
  textArray.push(`${value.cpu ? `${value.cpu / 1000}核CPU` : ''}`);
  textArray.push(`${value.memory ? `${value.memory / 1024}GB内存` : ''}`);

  return textArray;
};

export const columns = [
  {
    title: '任务名称',

    dataIndex: 'name',
    key: 'name',
    fixed: 'left',
    ellipsis: true,
  },
  {
    title: '创建人',
    dataIndex: 'name',
    key: 'name',
    ellipsis: true,
  },
  {
    title: '任务状态',
    dataIndex: 'status',
    key: 'status',
    filtersInit: STATUS_FILTER,
  },
  {
    title: '评估模型',

    dataIndex: 'modelInfo',
    key: 'modelInfo',
    ellipsis: true,
    customRender: ({ record }) => {
      return (record?.modelInfo || []).map(({ modelName }) => (modelName ? modelName : '--')).join();
    },
  },

  {
    title: '打分模式',

    dataIndex: 'scoringRule',
    key: 'scoringRule',
    ellipsis: true,
  },
  {
    dataIndex: 'creator',
    key: 'creator',
  },
  {
    title: '创建时间',
    sorter: true,
    dataIndex: 'subTime',
    customRender: ({ record }) => (record.subTime ? getFormatTime(record.subTime) : '--'),
  },
  {
    title: '所属资源组',

    dataIndex: 'resGroupName',
    key: 'resGroupName',
    filterKey: 'resGroupId',
    filters: [],
    ellipsis: true,
  },
  {
    title: '任务资源配置',

    dataIndex: 'resourceInfo',
    key: 'resourceInfo',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'options',
    key: 'options',
    fixed: 'right',
    ellipsis: true,
  },
];
