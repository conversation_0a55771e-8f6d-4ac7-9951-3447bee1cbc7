<template>
  <a-spin :spinning="globalLoading" wrapper-class-name="app-loading-spin">
    <template #indicator>
      <RectLoading />
    </template>
    <template #tip>
      <div class="tip-content">
        <h1 class="title">{{ globalLoadingTitle }}</h1>
        <p class="text">{{ globalLoadingText }}</p>
      </div>
    </template>
    <template v-for="(item, key, index) in $slots" :key="index" #[key]>
      <slot :name="key"></slot>
    </template>
  </a-spin>
</template>

<script setup>
// 用法
// store.dispatch('updateGlobalLoadingTitle', '项目空间创建中');
// store.dispatch('updateGlobalLoadingText', '创建项目空间大约需要几分钟，请耐心等待');
// store.dispatch('updateGlobalLoading', true);
// const res = await requestWithProjectId.POST('/web/project/v1/project/create', params);
// store.dispatch('updateGlobalLoading', false);
// store.dispatch('updateGlobalLoadingTitle', '');
// store.dispatch('updateGlobalLoadingText', '');

import { useStore } from 'vuex';
import RectLoading from './RectLoadingIcon.vue';

const store = useStore();
const globalLoading = computed(() => store.state.globalLoading);
const globalLoadingText = computed(() => store.state.globalLoadingText);
const globalLoadingTitle = computed(() => store.state.globalLoadingTitle);
</script>

<style lang="less" scoped>
:deep(.ant-spin .ant-spin-dot) {
  width: auto;
  height: auto;
}
.tip-content {
  padding-top: 40px;
}
.title {
  margin-bottom: 10px;
  font-weight: 400;
  font-size: 20px;
  color: #00141a;
}
.text {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 20, 26, 0.7);
}
.app-loading-spin {
  min-height: 100vh;
}
</style>
