<template>
  <div class="xterm-container">
    <div ref="xtermElement" class="xterm-instance"></div>
  </div>
</template>

<script setup>
import 'xterm/css/xterm.css';
import { Terminal } from 'xterm';
import { FitAddon } from 'xterm-addon-fit';
import { AttachAddon } from 'xterm-addon-attach';
import { onMounted, onBeforeUnmount, defineProps, defineEmits, defineExpose, ref, watch, nextTick } from 'vue';
//import screenfull from 'screenfull';
import { message } from 'ant-design-vue';
const INVALID_REASON = 'url invalid';
import { requestWithProjectId } from '@/request';
const { POST: requestWithProjectIdPOST } = requestWithProjectId;
import { keycloak } from '@/keycloak';

const props = defineProps({
  url: {
    // 直接可以访问的socket url
    type: String,
    default: '',
  },
  requestUrl: {
    // 从请求中获取url的方式
    type: String,
    default: '',
  },
  requestData: {
    type: Object,
    default: () => ({}),
  },
  currentKey: {
    type: String,
  },
  defaultHeight: {
    type: String,
    default: 'calc(100vh - 261px)',
  },
  isFullScreen: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(['change']);

let xtermInstance = null;
let socket = null;
let attachAddon = null;

const xtermElement = ref(null);
let createUrl = null;
let isDestroy = false; // 防止组件退出时有close提示

let previousCols = 0;
let previousRows = 0;

// 根据是否全屏自适应终端的高度
const FULLSCREEN_HEIGHT = 'calc(100vh - 106px)';

const containerHeight = ref(props.isFullScreen ? FULLSCREEN_HEIGHT : props.defaultHeight);

onMounted(async () => {
  buildWebsocketConnect();
});

onBeforeUnmount(() => {
  destroySocket();
  // if (screenfull.isEnabled) {
  //   screenfull.off('change');
  // }
});

// 处理tab切换时，需要重新出发下resize，因为在这期间会有窗口大小的改变或a-tab导致的display变化
// watch(
//   () => props.currentKey,
//   () => {
//     resizeTerm();
//   }
// );

const buildWebsocketConnect = async () => {
  if (!props.url) {
    const res = await requestWithProjectIdPOST(props.requestUrl, props.requestData);
    if (res.code === 0) {
      createUrl = res.data;
    }
  }
  createWebSocket();
  initXtermInstance();
  window.addEventListener('resize', resizeTerm);
  initScreenChangeEvent();
};

const destroySocket = () => {
  isDestroy = true;
  socket && socket.close();
  xtermInstance && xtermInstance.dispose();
  socket = null;
  xtermInstance = null;
  previousCols = 0;
  previousRows = 0;
  window.removeEventListener('resize', resizeTerm);
};

const reConnectWebsocket = () => {
  destroySocket();
  buildWebsocketConnect();
};

const initScreenChangeEvent = () => {
  // if (screenfull.isEnabled) {
  //   screenfull.on('change', () => {
  //     if (screenfull.isFullscreen) {
  //       containerHeight.value = FULLSCREEN_HEIGHT;
  //     } else {
  //       containerHeight.value = props.defaultHeight;
  //     }
  //     nextTick(resizeTerm);
  //   });
  // }
  if (props.isFullScreen) {
    containerHeight.value = FULLSCREEN_HEIGHT;
  } else {
    containerHeight.value = props.defaultHeight;
  }
  //nextTick(resizeTerm);
};

// 支持父组件全屏设置shell窗口
const handleFullscreen = () => {
  // if (screenfull.isEnabled) {
  //   screenfull.toggle(xtermElement.value);
  // }
};
// 创建xterm实例
const initXtermInstance = () => {
  xtermInstance = new Terminal({
    rendererType: 'canvas', //渲染类型
    disableStdin: false, //是否应禁用输入
    cursorStyle: 'underline', //光标样式
    cursorBlink: true, //光标闪烁
    scrollback: 800, //回滚
    letterSpacing: 2,

    theme: {
      foreground: '#ffffff', //字体
      background: '#060101', //背景色
      cursor: 'help', //设置光标
    },
  });
  attachAddon = new AttachAddon(socket);
  const fitAddon = new FitAddon();
  xtermInstance.loadAddon(attachAddon);
  xtermInstance.loadAddon(fitAddon);
  xtermInstance.open(xtermElement.value);
  fitAddon.fit();
  xtermInstance.focus();
};
// 创建websocket连接
const createWebSocket = () => {
  emit('change', socket);
  if (socket && !socket.readyState) {
    socket.close();
  }
  socket = new WebSocket(props.url || createUrl, [encodeURIComponent(`Bearer ${keycloak.token}`)]);
  socket.onopen = () => {
    emit('change', socket);
    resizeTerm();
  };

  socket.onclose = (event) => {
    emit('change', socket);
    if (isDestroy) {
      return;
    }
    if (event.code === 1007 && event.reason === INVALID_REASON) {
      message.error('链接已失效，请刷新重试');
    } else {
      message.error('连接异常，请刷新重试');
    }
  };
};

const calculateSize = () => {
  const box = xtermElement.value;
  const width = box.offsetWidth;
  const height = box.offsetHeight;
  // 在不同屏幕下，行高出现了1px的差，导致输入框高度异常
  const rowHeight = getRowHeight(box);
  const rows = parseInt(height / rowHeight);
  const cols = parseInt(width / 11);
  return {
    cols,
    rows,
  };
};

const getRowHeight = (box) => {
  const height = box.getElementsByClassName('xterm-rows')[0]?.children[0]?.style.height || '0';
  return Number(height.replace('px', '')) || 17;
};

// 根据窗口变化，动态调整shell窗口的大小
const resizeTerm = () => {
  const { cols, rows } = calculateSize();
  if ((cols === previousCols && rows === previousRows) || cols <= 0 || rows <= 0) {
    return;
  } else {
    previousCols = cols;
    previousRows = rows;
  }
  const msg = ['set_size', cols, rows];
  xtermInstance && xtermInstance.resize(cols, rows);
  if (socket && socket.readyState === WebSocket.OPEN) {
    socket.send(JSON.stringify(msg));
  }
  xtermInstance && xtermInstance.scrollToBottom();
};

defineExpose({
  reConnectWebsocket,
  handleFullscreen,
});
</script>

<style lang="less" scoped>
.xterm-container {
  height: v-bind(containerHeight);
  background: #060101;
  .xterm-instance {
    height: 100%;
  }
  .fullscreen {
    position: absolute;
    right: 30px;
    top: 20px;
  }
}
</style>
