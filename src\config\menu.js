import { check<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, checkApvAndMgtPermission, checkPoolSettings, checkPathByRole, checkPathBySasac, checkPathByPlatform } from '@/utils/auth';
import store from '@/store';
import { APV_QUATO_VIEW_AUTH, MGT_QUATO_VIEW_AUTH } from '@/constants/quota';
import { MGT_RESGROUP_VIEW_AUTH } from '@/constants/resourceGroup';
import { BOARD_RES_VIEW_AUTH, BOARD_PRJ_VIEW_AUTH, BOARD_ACTIVITY_VIEW_AUTH, BOARD_EXP_VIEW_AUTH } from '@/constants/board';
import { PLAT_OVER_VIEW_AUTH, PLAT_POOL_VIEW_AUTH, PLAT_POOL_EDIT_AUTH } from '@/constants/management-platform/auth';
import { MGT_PRJACT_VIEW_AUTH } from '@/views/management-center/activity-manage/auth';
import { MGT_STODIR_VIEW_AUTH } from '@/constants/storageDirManager';
import { MGT_NOTICE_VIEW_AUTH } from '@/constants/announcement';
import { MGT_USAGE_VIEW_AUTH } from '@/constants/usage';
import { PRESET_ASSETS_VIEW_AUTH } from '@/constants/presetAssets';
import { MGT_LOG_VIEW_AUTH } from '@/constants/operationLog';
import { MGT_CORPUS_VIEW_AUTH } from '@/constants/corpusManage';
import { DATA_SECURITY_VIEW_AUTH } from '@/constants/dataSecurity';
import { ALARM_VIEW_AUTH } from '@/constants/alarmMap';
import { LOG_MGR_VIEW_AUTH } from '@/constants/log-mgr';
import { getMenuKey } from '@/utils/index';
export const ENV_PATH = {};
export const KEYCLOAK_PATH = {};

// 资源池对应的审批与管理权限，需要和@贺渊的keycloak配置里的key对应上
export const POOL_APV_MGT_PERMISSION = {
  'quota-manage': [APV_QUATO_VIEW_AUTH, MGT_QUATO_VIEW_AUTH],
  'res-dashboard': [BOARD_RES_VIEW_AUTH, BOARD_PRJ_VIEW_AUTH, BOARD_ACTIVITY_VIEW_AUTH, BOARD_EXP_VIEW_AUTH],
  'resource-group': [MGT_RESGROUP_VIEW_AUTH],
  'activity-manage': [MGT_PRJACT_VIEW_AUTH],
  'storage-dir-manager': [MGT_STODIR_VIEW_AUTH],
  announcement: [MGT_NOTICE_VIEW_AUTH],
  'usage-manage': [MGT_USAGE_VIEW_AUTH],
  'preset-asset': [PRESET_ASSETS_VIEW_AUTH],
  'operation-log': [MGT_LOG_VIEW_AUTH],
  'corpus-manage': [MGT_CORPUS_VIEW_AUTH],
  'data-security': [DATA_SECURITY_VIEW_AUTH],
  'alarm-manage': [ALARM_VIEW_AUTH],
  'log-mgr': [LOG_MGR_VIEW_AUTH],
};
// 运管平台对于的操作权限，需要和@贺渊的keycloak配置里的key对应上(且相关菜单不在资源池范围内)
export const PLAT_FORM_APV_MGT_PERMISSION = {
  'manage-overview': [PLAT_OVER_VIEW_AUTH], // 运营概览
  'resource-pool-manage': [PLAT_POOL_VIEW_AUTH], // 资源池管理
};

// 资源池下配置的菜单可见权限,需要和@年鉴的接口中的key对应上
export const POOL_SETTING_VISIBLE_PERMISSION = {
  'data-cleaning': 'datasetClean', // 数据清洗
  'model-experience': 'modelExperience', // 模型体验
  'data-analysis': 'datasetAnalyse', // 数据解析
  'data-annotation': 'datasetAnnotation', // 数据标注
  'data-augmentation': 'datasetEnhance', // 数据增强
  'data-quality-inspection': 'datasetQi', // 数据质检
  'preference-alignment': 'preferAlign', // 偏好对齐
  'corpus-statistics': 'corpusStatistics', // 语料收集统计
  'corpus-manage': 'corpusManage', // 语料收集管理
  'data-security': 'dataSecurityManage', // 数据安全管理
  'train-task': 'trainTask', // 训练任务
  'train-dev': 'trainDev', // 开发环境
  dataset: 'datasetManage', // 数据集管理
  'data-reflux': 'datasetReflux', // 数据回流
  'model-square': 'modelSquare', // 模型广场
  'incremental-pretrain': 'postPretrain', // 增量预训练
  'supervised-finetune': 'supervisedFinetune', // 有监督微调
  'model-distillation': 'knowledgeDistillation', // 模型蒸馏
  compressManage: 'modelCompress', // 模型压缩
  'model-evaluation': 'modelEvaluation', // 模型评估
  'service-manage': 'inferService', // 推理服务
  'application-access': 'appAccess', // 应用接入
  'call-statistics': 'tokenStatistics', // 调用统计
  modelManage: 'modelManage', // 模型管理
  mirrorManage: 'imageManage', // 镜像管理
  'file-manage': 'fileManage', // 文件管理
  'prompt-engineering': 'promptManage', // 提示词管理
  'project-usage-manage': 'usageManage', // 用量管理
  'project-alarm-manage': 'alertManage', // 告警管理
  'project-log-manage': 'operationLog', // 操作日志
  // 前端控制默认权限 -- 默认显示
  announcement: 'announcement', // 公告管理
  'activity-manage': 'activityManage', // 项目活动管理
  'preset-asset': 'presetAssets', // 预置资产管理
  'quota-manage': 'quotaManage', // 配额管理
  'res-dashboard': 'resDashboard', // 运营看板
  'resource-group': 'resGroup', // 资源组管理
  'storage-dir-manager': 'storageDirManager', // 存储目录管理
  'log-mgr': 'logManage', // 日志管理
};
// 无需配置化菜单，默认可见
export const POOL_MENU_PERMISSION = {
  announcement: '1',
  activityManage: '1',
  presetAssets: '1',
  quotaManage: '1',
  resDashboard: '1',
  resGroup: '1',
  storageDirManager: '1',
};
// 运管中心菜单-tab关联
export const MENU_TABS_CONFIG = {
  'activity-manage': ['datasetAnalyse', 'postPretrain', 'datasetClean', 'datasetEnhance', 'trainDev', 'trainTask', 'supervisedFinetune', 'preferAlign', 'modelCompress', 'modelEvaluation', 'inferService'], // 数据解析 增量预训练 数据清洗 数据增强 开发环境 训练任务 有监督微调 偏好对齐 模型压缩 模型评估 推理服务
};

export async function formatSideMenu(menuData) {
  const sideMenuAuth = [];

  function checkPathVisible(path) {
    // 补充运管平台判断场景
    if (store.state.menuHasPlatform) return checkKeycloakPathAuth(path) && checkApvAndMgtPermission(path) && checkPathByPlatform(path);
    return checkPathBySasac(path) && checkPoolSettings(path) && checkKeycloakPathAuth(path) && checkApvAndMgtPermission(path) && checkPathByRole(path);
  }

  // 先获取所有有权限的菜单
  menuData.forEach((element) => {
    if (element.subs.length > 0) {
      element.subs.forEach((sub) => {
        const path = getMenuKey(sub.link);
        if (checkPathVisible(path)) {
          sideMenuAuth.push(path);
        }
      });
    } else {
      const path = getMenuKey(element.link);
      if (checkPathVisible(path)) {
        sideMenuAuth.push(path);
      }
    }
  });
  // 再根据有权限的菜单，筛选出需要展示的菜单
  const sideMenuData = [];
  menuData.forEach((menuItem) => {
    if (menuItem.subs.length > 0) {
      const filteredSubs = menuItem.subs.filter((subMenu) => sideMenuAuth.includes(getMenuKey(subMenu.link)));
      if (filteredSubs.length > 0) {
        sideMenuData.push({
          ...menuItem,
          subs: filteredSubs,
        });
      }
    } else if (sideMenuAuth.includes(getMenuKey(menuItem.link))) {
      sideMenuData.push(menuItem);
    }
  });

  return { sideMenuAuth, sideMenuData };
}
