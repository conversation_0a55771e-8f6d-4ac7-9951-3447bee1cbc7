import defaultConfig from './default.config';
import { proxyPrefix, proxyTargetHost } from './proxy.config';

export function getDefaultConfig() {
  const config = { ...defaultConfig };
  config.KEYCLOAK_URL = `${proxyTargetHost}${config.KEYCLOAK_URL}`;
  return config;
}

export function getEnvConfig(key) {
  const config = getEnvConfigFromBody(key) || getDefaultConfig()[key];
  return config;
}

function getEnvConfigFromBody(key) {
  const target = document.querySelector('body');
  const value = target?.getAttribute(key.replace(/_/g, ''));
  return value || '';
}

export function getProxyPrefix() {
  return proxyPrefix;
}

// keycloak url 地址
export const helpcenterUrlPathEnv = getEnvConfig('HELPCENTER_URL_PATH'); // 帮助中心路径

export const homePageUrl = getEnvConfig('HOME_PAGE_URL'); // 左上角logo点击跳转地址，当为null时，代表不支持点击跳转

export const showEmail = getEnvConfig('FEATURE_EMAIL') === '1'; // 是否支持邮件服务

export const showSMS = getEnvConfig('FEATURE_SMS') === '1'; // 是否支持短信服务

export const showShareDataset = getEnvConfig('SHOW_DATASET_SHARE') === '1'; // 是否支持分享数据集

export const showAccountManagement = getEnvConfig('SHOW_ACCOUNT_MANAGEMENT') === '1'; // 是否显示账号管理

export const portalPrefixUrl = getEnvConfig('PORTAL_PREFIX_URL'); // 访问九天门户资源的前缀

export const showDashboard = getEnvConfig('DASHBOARD') === '1'; // 是否显示大屏相关入口，页面
