import { requestWithProjectId } from '@/request/index';
const { POST, GET } = requestWithProjectId;

// 应用 id 获取已关联的在线服务&服务群组列表
export const getBindServiceList = async (payload) => {
  return GET(`/web/serving/v1/app/${payload.appId}/instance/list`, payload);
};

// 应用 id 获取绑定的推理服务
export const getGroupList = async ({ appId, ...payload }) => {
  return GET(`/web/serving/v1/app/${appId}/group/list`, payload);
};

// 解绑 服务群组 groupIds
export const unBindGroup = async (payload) => {
  return POST('/web/serving/v1/app/group/unbind', payload);
};

// 绑定 在线服务 instanceIds & 服务群组 groupIds
export const bindGroup = async (payload) => {
  return POST('/web/serving/v1/app/instance/bind', payload);
};

// 根据群组 id 获取应用数据
export const getGroupListFormApp = async (payload) => {
  return GET('/web/serving/v1/app/group/info', payload);
};
