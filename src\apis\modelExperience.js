import { requestWithProjectId } from '@/request/index';
const { POST, GET, PUT, DELETE } = requestWithProjectId;

//模型对话
export const reqPicTotext = (paramObj) => {
    return POST(`/web/experience/v1/chat/image-to-text`, paramObj)
}
export const reqTextToPic = (paramObj) => {
    return POST(`/web/experience/v1/chat/text-to-image`, paramObj)
}

export const reqLike = (messageId) => {
    return POST(`/web/experience/v1/chat/like?messageId=${messageId}`)
}

export const reqDislike = (messageId) => {
    return POST(`/web/experience/v1/chat/dislike?messageId=${messageId}`)
}

export const reqBase64 = (messageId) => {
    return POST(`/web/experience/v1/chat/text-to-speech?messageId=${messageId}`)
}

export const reqStop = (messageId) => {
    return POST(`/web/experience/v1/chat/stop?messageId=${messageId}`)
}

//历史记录
export const reqHistoryList = (paramObj) => {
    const { chatType, keyword } = paramObj
    const newKeyWord = encodeURIComponent(keyword)
    return GET(`/web/experience/v1/history/list?chatType=${chatType}&keyword=${newKeyWord}`)
}

export const reqDelHistoryCase = (groupId,chatId) => {
    return DELETE(`/web/experience/v1/history/${groupId}?chatId=${chatId}`)
}

export const reqEditHistoryCase = (param) => {
    const { groupId, changedName } = param
    const newChangedName = encodeURIComponent(changedName)
    return PUT(`/web/experience/v1/history/name?groupId=${groupId}&changedName=${newChangedName}`)
}

export const reqDialogDetail = (groupId) => {
    return GET(`/web/experience/v1/history/detail?groupId=${groupId}`)
}

//模型配置
export const reqPresetModelList = () => {
    return GET(`/web/model/manage/preset/v1/list-for-experience`)
}

export const reqChartParam = (id) => {
    return GET(`/web/model/manage/preset/v1/config?id=${id}&moduleConfigEnum=MODEL_EXPERIENCE`)
}

export const reqCustomService = () => {
    return GET(`/web/experience/v1/chat/custom-service`)
}

