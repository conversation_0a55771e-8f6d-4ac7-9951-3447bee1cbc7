import { resourceApi } from '@/apis';
import ErrorCode from '@/constants/errorCode';
import { STATUS_TEXT_MAP, ANALYSIS_STATUS_TEXT } from '@/constants/index';
// 任务类型枚举
export const TASK_TYPE = {
  DEV: 'develop',
  TASK: 'task',
  INFE: 'serving',
  // 增量预训练、有监督微调、模型压缩、模型评估(v1.3新增)
  DAP: 'dap',
  SFT: 'sft',
  COMPRESS: 'compress',
  EVALUATION: 'evaluation',
  // 数据清洗、数据解析、数据增强、偏好对齐(v.2.0)
  CLEAN: 'clean',
  ANALYSIS: 'analysis',
  ANGMENTATION: 'angmentation',
  DPO: 'dpo',
  KD: 'kd',
};

// 训练任务 优先级
export const PRIORITY_TYPE = {
  HIGH: 'high',
  MED: 'med',
  LOW: 'low',
};
// 训练任务 优先级对应文案
export const PRIORITY_TYPE_MSG = {
  [PRIORITY_TYPE.HIGH]: '高',
  [PRIORITY_TYPE.MED]: '中',
  [PRIORITY_TYPE.LOW]: '低',
};

// 增量预训练、有监督微调、模型压缩、偏好对齐
export const TASK_COMMON_STATUS = {
  onSTART: 1, // 启动中
  QUEUE: 2, // 排队中
  RUNNING: 3, // 运行中
  SUCCESS: 4, // 成功
  FAIL: 6, // 失败
  onSTOP: 5, // 停止中
  STOP: 7, // 已停止
};

// 模型评估的状态
export const EVALUATION_TASK_STATUS = {
  onSTART: 1, // 启动中
  QUEUE: 2, // 排队中
  RUNNING: 3, // 运行中
  SUCCESS: 4, // 成功
  FAIL: 5, // 失败
  onSTOP: 6, // 停止中
  STOP: 7, // 已停止
  WAITING: 8, // 待评估
};

// 数据解析状态
export const DATA_ANALYSIE_STATUS = {
  RUNNING: 1, // 运行中
  QUEUE: 2, // 排队中
  onSTOP: 3, // 停止中
  STOP: 4, // 已停止
  FINISHED: 5, // 成功
  FAIL: 6, // 失败
};

// 数据清洗、增强状态
export const CLEAN_AND_ENHANCE_STATUS = {
  QUEUE: 2, // 排队中
  RUNNING: 3, // 运行中
  STOP: 5, // 已停止
  FAIL: 6, // 失败
  SUCCESS: 4, // 成功
};

export const TASK_TYPE_MSG = {
  [TASK_TYPE.DEV]: '开发环境',
  [TASK_TYPE.TASK]: '训练任务',
  [TASK_TYPE.INFE]: '在线服务',
};
// 镜像类型
export const IMAGE_TYPE = {
  PRESET: 'preset', // 平台预制镜像
  CUSTOM: 'personal', // 自定义镜像
};
// 镜像类型对应的文案
export const IMAGE_TYPE_MSG = {
  [IMAGE_TYPE.PRESET]: '平台预置镜像',
  [IMAGE_TYPE.CUSTOM]: '自定义镜像',
};
// 页面来源
export const ORIGIN_MAP = {
  DEFAULT: '1', // 当前（大模型平台）
  HUIJU: '2', // 汇聚平台
};
// 资源组类型
export const RESOURCE_TYPE = {
  CPU: 'cpu', // CPU
  GPU: 'gpucard', // 加速卡
};
export const RESOURCE_TYPE_MSG = {
  [RESOURCE_TYPE.CPU]: 'CPU',
  [RESOURCE_TYPE.GPU]: '加速卡',
};
// 资源组大类（公共资源组、专属资源组）
export const RESOURCE_GROUP_TYPE = {
  PUBLIC: 'public', // 公共资源组
  PERSONAL: 'personal', // 专属资源组
};
export const RESOURCE_STATUS = {
  FREE: '空闲',
  CROWD: '拥挤',
  BUSY: '占满',
};
// 实例监控时间枚举值
export const MONITOR_TIME_MAP = {
  FIVE_MIN: 'fm',
  HALF_HOUR: 'hh',
  ONE_HOUR: 'oh',
  ONE_DAY: 'od',
};
// 当前模型训练（新建时）的状态
export const TRAINTASK_STATUS = {
  EDIT: 0,
  COPY: 1,
};

// 在模型训练的详情返回中区分是否为分享数据集（是否为分享数据集都会在自定义数据集列表中返回）
export const SHARE_DATASET_SOURCE = {
  NO_SHARE: 0, // 非分享数据集
  SHARE: 1, // 分享数据集
};

export function RESOURCE_STATUS_MSG(value) {
  let msg = '占满';
  switch (value) {
    case value >= 0 && value < 50:
      msg = '空闲';
      break;
    case value >= 50 && value < 100:
      msg = '拥挤';
      break;
    default:
      break;
  }
  return msg;
}

// 开发环境、训练任务状态表
export const STATUS = {
  [TASK_TYPE.DEV]: {
    onSTART: 1, // 启动中
    RUNNING: 2, // 运行中
    onSTOP: 3, // 停止中
    STOP: 4, // 已停止
    FAIL: 5, // 失败
    SNAPSHOT: 6, // 快照中
    LOCKED: 7, // 已锁定
  },
  [TASK_TYPE.TASK]: {
    onSTART: 1, // 启动中
    QUEUE: 2, // 排队中
    RUNNING: 3, // 运行中
    onSTOP: 4, // 停止中
    STOP: 5, // 已停止
    FAIL: 6, // 失败
    STARTED: 0, //已启动、成功
  },
  [TASK_TYPE.INFE]: {
    onSTART: 'starting', // 启动中
    UPDATING: 'updating', // 更新中
    RUNNING: 'running', // 运行中
    onSTOP: 'stopping', // 停止中
    STOP: 'stopped', // 已停止
    FAIL: 'error', // 失败
    PARTIALLYRUNNING: 'partiallyRunning', // 部分运行中
  },
  [TASK_TYPE.KD]: TASK_COMMON_STATUS,
  [TASK_TYPE.DAP]: TASK_COMMON_STATUS,
  [TASK_TYPE.SFT]: TASK_COMMON_STATUS,
  [TASK_TYPE.COMPRESS]: TASK_COMMON_STATUS,
  [TASK_TYPE.EVALUATION]: EVALUATION_TASK_STATUS,
  [TASK_TYPE.CLEAN]: CLEAN_AND_ENHANCE_STATUS,
  [TASK_TYPE.ANALYSIS]: DATA_ANALYSIE_STATUS,
  [TASK_TYPE.ANGMENTATION]: CLEAN_AND_ENHANCE_STATUS,
  [TASK_TYPE.DPO]: TASK_COMMON_STATUS,
};

export const getTrainStatusMarkStyle = (type, status) => {
  const statusClassnamemap = new Map([
    [STATUS[type]?.QUEUE, 'warning'],
    [STATUS[type]?.QUEUE, 'warning'],
    [STATUS[type]?.onSTART, 'warning'],
    [STATUS[type]?.onSTOP, 'warning'],
    [STATUS[type]?.SNAPSHOT, 'warning'],
    [STATUS[type]?.LOCKED, 'warning'],
    [STATUS[type]?.STARTED, 'complete'],
    [STATUS[type]?.RUNNING, 'processing'],
    [STATUS[type]?.SUCCESS, 'complete'],
    [STATUS[type]?.FAIL, 'error'],
    [STATUS[type]?.STOP, 'default'],
    [STATUS[type]?.WAITING, 'default'],
  ]);
  const getStatus = isNaN(+status) ? status : +status;
  if (!statusClassnamemap.has(getStatus)) {
    return getStatus;
  }
  return statusClassnamemap.get(getStatus);
};

export const getTrainStatusMarkWord = (type, status) => {
  const statusWordmap = new Map([
    [STATUS[type].UPDATING, STATUS_TEXT_MAP.UPDATING],
    [STATUS[type].QUEUE, STATUS_TEXT_MAP.QUEUE],
    [STATUS[type].onSTART, STATUS_TEXT_MAP.STARTING],
    [STATUS[type].onSTOP, type === TASK_TYPE.ANALYSIS ? ANALYSIS_STATUS_TEXT.STOPPING : STATUS_TEXT_MAP.STOPPING],
    [STATUS[type].RUNNING, STATUS_TEXT_MAP.RUNNING],
    [STATUS[type].SUCCESS, STATUS_TEXT_MAP.SUCCESS],
    [STATUS[type].FAIL, type === TASK_TYPE.INFE ? STATUS_TEXT_MAP.RETRYING : STATUS_TEXT_MAP.FAIL],
    [STATUS[type].STOP, type === TASK_TYPE.ANALYSIS ? ANALYSIS_STATUS_TEXT.STOPPED : STATUS_TEXT_MAP.STOPPED],
    [STATUS[type].STARTED, STATUS_TEXT_MAP.SUCCESS],
    [STATUS[type].SNAPSHOT, STATUS_TEXT_MAP.SNAPSHOT],
    [STATUS[type].LOCKED, STATUS_TEXT_MAP.LOCKED],
    [STATUS[type].PARTIALLYRUNNING, STATUS_TEXT_MAP.PARTIALLYRUNNING],
    [STATUS[type].FINISHED, STATUS_TEXT_MAP.FINISHED],
    [STATUS[type].WAITING, STATUS_TEXT_MAP.WAITING],
  ]);
  const getStatus = isNaN(+status) ? status : +status;
  if (!statusWordmap.has(getStatus)) {
    return getStatus;
  }
  return statusWordmap.get(getStatus);
};

export const tableStatusFilter = (type) => {
  if (type === TASK_TYPE.DEV) {
    return [
      {
        text: STATUS_TEXT_MAP.RUNNING,
        value: STATUS[TASK_TYPE.DEV].RUNNING,
      },
      {
        text: STATUS_TEXT_MAP.STARTING,
        value: STATUS[TASK_TYPE.DEV].onSTART,
      },
      {
        text: STATUS_TEXT_MAP.SNAPSHOT,
        value: STATUS[TASK_TYPE.DEV].SNAPSHOT,
      },
      {
        text: STATUS_TEXT_MAP.STOPPING,
        value: STATUS[TASK_TYPE.DEV].onSTOP,
      },
      {
        text: STATUS_TEXT_MAP.STOPPED,
        value: STATUS[TASK_TYPE.DEV].STOP,
      },
      {
        text: STATUS_TEXT_MAP.LOCKED,
        value: STATUS[TASK_TYPE.DEV].LOCKED,
      },
      {
        text: STATUS_TEXT_MAP.FAIL,
        value: STATUS[TASK_TYPE.DEV].FAIL,
      },
    ];
  }
  if (type === TASK_TYPE.TASK) {
    return [
      {
        text: STATUS_TEXT_MAP.RUNNING,
        value: STATUS[TASK_TYPE.TASK].RUNNING,
      },
      {
        text: STATUS_TEXT_MAP.STARTING,
        value: STATUS[TASK_TYPE.TASK].onSTART,
      },
      {
        text: STATUS_TEXT_MAP.QUEUE,
        value: STATUS[TASK_TYPE.TASK].QUEUE,
      },
      {
        text: STATUS_TEXT_MAP.STOPPING,
        value: STATUS[TASK_TYPE.TASK].onSTOP,
      },
      {
        text: STATUS_TEXT_MAP.STOPPED,
        value: STATUS[TASK_TYPE.TASK].STOP,
      },
      {
        text: STATUS_TEXT_MAP.SUCCESS,
        value: STATUS[TASK_TYPE.TASK].STARTED,
      },
      {
        text: STATUS_TEXT_MAP.FAIL,
        value: STATUS[TASK_TYPE.TASK].FAIL,
      },
    ];
  }
  if (type === TASK_TYPE.INFE) {
    return [
      {
        text: STATUS_TEXT_MAP.STARTING,
        value: STATUS[TASK_TYPE.INFE].onSTART,
      },
      {
        text: STATUS_TEXT_MAP.PARTIALLYRUNNING,
        value: STATUS[TASK_TYPE.INFE].PARTIALLYRUNNING,
      },
      {
        text: STATUS_TEXT_MAP.RUNNING,
        value: STATUS[TASK_TYPE.INFE].RUNNING,
      },
      {
        text: STATUS_TEXT_MAP.STOPPING,
        value: STATUS[TASK_TYPE.INFE].onSTOP,
      },
      {
        text: STATUS_TEXT_MAP.STOPPED,
        value: STATUS[TASK_TYPE.INFE].STOP,
      },
      {
        text: STATUS_TEXT_MAP.RETRYING,
        value: STATUS[TASK_TYPE.INFE].FAIL,
      },
      {
        text: STATUS_TEXT_MAP.UPDATING,
        value: STATUS[TASK_TYPE.INFE].UPDATING,
      },
    ];
  }
};

// 表格 单实例资源配置 列内容显示
export const getResourceInfoWord = (value, taskType) => {
  let textArray = [];
  if (taskType !== TASK_TYPE.INFE) {
    if (+value?.gpu) {
      textArray.push(`${value?.gpu}加速卡`);
    }
    textArray.push(`${value?.cpu ? `${value?.cpu}核CPU` : ''}`);
    textArray.push(`${value?.mem ? `${value?.mem}GB内存` : ''}`);
  } else {
    if (+value?.gpu) {
      textArray.push(`${value?.gpu}加速卡`);
    }
    textArray.push(`${value?.cpu ? `${value?.cpu / 1000}核CPU` : ''}`);
    textArray.push(`${value?.memory ? `${value?.memory / 1024}GB内存` : ''}`);
  }
  return textArray;
};
// 一个参数情况
export const getResourceInfoWords = (value) => {
  let textArray = [];
  // if (taskType !== TASK_TYPE.INFE) {
  if (+value.gpu) {
    textArray.push(`${value.gpu}加速卡`);
  }
  textArray.push(`${value.cpu ? `${value.cpu}核CPU` : ''}`);
  textArray.push(`${value.mem ? `${value.mem}GB内存` : ''}`);
  return textArray;
};
// // 表格 单实例资源配置 列内容显示
// export const getResourceInfoWord = (value, taskType) => {
//   let text = ``;
//   if (taskType !== TASK_TYPE.INFE) {
//     if (+value.gpu) {
//       text += `${value.gpu}加速卡\xa0\xa0|\xa0\xa0`;
//     }
//     text += `${value.cpu ? `${value.cpu}核CPU\xa0\xa0|\xa0\xa0` : ''}${value.mem ? `${value.mem}GB内存` : ''}`;
//   } else {
//     if (+value.gpu) {
//       text += `${value.gpu}加速卡\xa0\xa0|\xa0\xa0`;
//     }
//     text += `${value.cpu ? `${value.cpu / 1000}核CPU\xa0\xa0|\xa0\xa0` : ''}${value.memory ? `${value.memory / 1024}GB内存` : ''}`;
//   }
//   return text;
// };

export const DEFAULT_DISPLAY_RULE = {
  optionsBtn: {
    open: true,
    edit: true,
    run: true,
    stop: true,
    delete: true,
    copy: true,
    tensorboard: true,
    priority: true,
  },
};

export const TRAIN_TYPE_DISPLAY_RULE = {
  [TASK_TYPE.DEV]: {
    ...DEFAULT_DISPLAY_RULE,
    optionsBtn: {
      ...DEFAULT_DISPLAY_RULE.optionsBtn,
      copy: false,
      tensorboard: false,
    },
  },
  [TASK_TYPE.TASK]: {
    ...DEFAULT_DISPLAY_RULE,
    optionsBtn: {
      ...DEFAULT_DISPLAY_RULE.optionsBtn,
      open: false, //训练任务不支持打开
      edit: false, //训练任务不支持编辑
      run: true,
      stop: true,
      delete: true,
      copy: true,
    },
  },
};

export function btnDisabled(type, record, taskType) {
  let disabled = false;
  const status = isNaN(+record.status) ? +record.state : +record.status;
  switch (type) {
    case 'open':
      disabled = ![STATUS[taskType].RUNNING].includes(status);
      break;
    case 'edit':
      disabled = ![STATUS[taskType].STOP, STATUS[taskType].LOCKED, STATUS[taskType].FAIL].includes(status);
      break;
    case 'stop':
      if (taskType === TASK_TYPE.DEV) {
        disabled = [STATUS[taskType].onSTOP].includes(status);
      } else {
        disabled = [STATUS[taskType].onSTOP, STATUS[taskType].STARTED].includes(status);
      }
      break;
    case 'run':
      disabled = [STATUS[taskType].onSTART].includes(status);
      break;
    case 'copy':
      disabled = false;
      break;
    case 'delete':
      if (taskType === TASK_TYPE.DEV) {
        disabled = ![STATUS[taskType].STOP, STATUS[taskType].LOCKED, STATUS[taskType].FAIL].includes(status);
      } else {
        disabled = false;
      }
      break;
    // 调整优先级
    case 'priority':
      disabled = ![STATUS[taskType].QUEUE, STATUS[taskType].STOP, STATUS[taskType].FAIL].includes(status);
      break;
  }
  return disabled;
}

export function showBtn(type, record, taskType) {
  let show = false;
  const status = isNaN(+record.status) ? +record.state : +record.status;
  switch (type) {
    case 'open':
      show = TRAIN_TYPE_DISPLAY_RULE[taskType].optionsBtn.open;
      break;
    case 'edit':
      show = TRAIN_TYPE_DISPLAY_RULE[taskType].optionsBtn.edit;
      break;
    case 'stop':
      if (taskType === TASK_TYPE.DEV) {
        show = [STATUS[taskType].RUNNING, STATUS[taskType].onSTOP].includes(status);
      } else {
        show = [STATUS[taskType].RUNNING, STATUS[taskType].onSTOP, STATUS[taskType].QUEUE, STATUS[taskType].STARTED].includes(status);
      }
      break;
    case 'run':
      show = [STATUS[taskType].STOP, STATUS[taskType].LOCKED, STATUS[taskType].FAIL, STATUS[taskType].onSTART].includes(status);
      break;
    case 'copy':
      show = TRAIN_TYPE_DISPLAY_RULE[taskType].optionsBtn.copy;
      break;
    case 'delete':
      show = TRAIN_TYPE_DISPLAY_RULE[taskType].optionsBtn.delete;
      break;
    // 可视化
    case 'tensorboard':
      show = TRAIN_TYPE_DISPLAY_RULE[taskType].optionsBtn.tensorboard && [STATUS[taskType].RUNNING].includes(status);
      break;
    // 调整优先级
    case 'priority':
      show = TRAIN_TYPE_DISPLAY_RULE[taskType].optionsBtn.priority;
      break;
  }
  return show;
}

export function taskApiInfo(type) {
  if (type === TASK_TYPE.INFE) {
    return {
      list: `/web/serving/v1/instance/list`, // 服务列表
    };
  }
  return {
    exist: `/web/${type}/v1/exist`, // 校验项目下是否有同名任务
    create: `/web/${type}/v1/create`, // 新建开发环境、训练任务
    start: `/web/${type}/v1/start`, // 运行开发环境、训练任务
    stop: `/web/${type}/v1/stop`, // 停止开发环境、训练任务
    list: `/web/${type}/v1/list`, // 开发环境、训练任务列表
    detail: `/web/${type}/v1/detail`, // 开发环境、训练任务详情
    adminDetail: type === TASK_TYPE.DEV ? '/web/admin/develop/v1/detail' : '/web/admin/task/v1/detail',
    delete: `/web/${type}/v1/delete`, // 删除开发环境、训练任务
    update: `/web/${type}/v1/update`, // 编辑开发环境、训练任务
    getPodList: `/web/${type}/v1/pod/list`, // 分页获取实例列表信息
    getAdminPodList: '/web/admin/task/v1/pod/list', // 运管训练任务的实例列表
    getMonitorData: `/web/${type}/v1/${type === TASK_TYPE.DEV ? 'instance' : 'pod'}/monitor`, // 实例监控
    getAdminMonitorData: type === TASK_TYPE.DEV ? 'web/admin/develop/v1/instance/monitor' : 'web/admin/task/v1/monitor', // 运管平台实例监控接口
    getTensorboard: type === TASK_TYPE.DEV ? `/web/develop/v1/tensorboard` : `/web/task/v1/pod/tensorboard`, // 模型训练详情-tensorboard
    getMindinsight: type === TASK_TYPE.DEV ? `/web/develop/v1/mindinsight` : `/web/task/v1/pod/mindinsight`, // 模型训练详情-mindinsight
  };
}

export const getFormatString = (_date) => {
  const date = new Date(_date);
  const H = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
  const M = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  const S = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  return `${H}:${M}:${S}`;
};

export function changeUseTime(useTime) {
  if (!/:/g.test(useTime)) {
    return useTime;
  }
  const itemTime = useTime?.split(':');
  let [h, m, s] = [Number(itemTime?.[0]), Number(itemTime?.[1]), Number(itemTime?.[2])];
  s++;
  if (s >= 60) {
    s = 0;
    m++;
  }
  if (m >= 60) {
    m = 0;
    h++;
  }
  const timeTxt = `${h < 10 ? '0' + h : h}:${m < 10 ? '0' + m : m}:${s < 10 ? '0' + s : s}`;
  return timeTxt;
}

/**
 * 列表的启动时长变更
 * @param {array} list 变更列表
 */
export const changeTableListUseTime = (list, taskType) => {
  for (const i in list) {
    const item = list[i];
    if (item.status == STATUS[taskType].RUNNING) {
      list[i].useTime = changeUseTime(item.useTime);
    }
  }
  return new Promise((resolve) => {
    resolve(list);
  });
};

export const getResourceGroupList = async () => {
  let resourceGroupList = [];
  try {
    const publicResourceGroupList = await resourceApi.getListAllResourceGroup();
    if (publicResourceGroupList.code === 0) {
      resourceGroupList = resourceGroupList.concat(publicResourceGroupList.data);
    }
  } catch (err) {
    throw new Error(err);
  }
  return Promise.resolve(resourceGroupList);
};

/**
 * 需要使用后端返回的错误文案的错误码集合
 * @returns {Array} 错误码
 */
export const trainErrorUseBackMsgArry = (() => {
  const devCodeList = [170036, 170037, 170040, 170041, 170050, , 240017, 240018];
  const taskCodeList = [151035, 151040, , 240017, 240018, 151058];
  return [...devCodeList, ...taskCodeList];
})();
/**
 * 根据错误码返回对应文案（除所需文案之外的错误码返回默认文案）
 * @param {string} code 错误码
 * @returns 错误码对应文案
 */
export const trainTaskErrorMsg = (code, msg = '') => {
  const devCodeList = [170014, 170015, 170016, 170017, 170025, 170036, 170037, 170038, 240017, 240018];
  const taskCodeList = [151009, 151010, 151011, 151035, 151040, 240017, 240018];
  const ableShowErrorCodeList = [...devCodeList, ...taskCodeList];
  if (trainErrorUseBackMsgArry.includes(code)) {
    return msg;
  }
  if (ableShowErrorCodeList.includes(code)) {
    return ErrorCode[code];
  }
  return '请稍后再试';
};

export const tagColor = (status) => {
  const statusColorMap = {
    运行中: 'green',
    启动中: 'orange',
    停止中: 'orange',
    已停止: 'default',
    成功: 'cyan',
    失败: 'red',
    重试中: 'red',
    排队中: 'quete-yellow-jt',
    快照中: 'orange',
    已锁定: 'default',
    部分运行中: 'green',
    已完成: 'cyan',
  };
  if (status && statusColorMap[status]) {
    return statusColorMap[status];
  }
  return 'default';
};
