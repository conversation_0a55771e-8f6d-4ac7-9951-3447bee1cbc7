import { GET, POST, requestWithPoolId } from '@/request';

// 运营总览-项目看板
export const getDashboardInfo = () => GET('/web/admin/project/v1/stat/plat-dashboard-info');

// 运营总览-用户活跃或项目活跃图
export const getActiveSummary = (params) => GET('/web/admin/project/v1/stat/list-plat-active-summary', params);

// 运营总览-活动概览-开发环境数据
export const getStatisticsData = () => GET('/web/admin/develop/v1/allPoolIndexStatistics');

// 运营总览-活动概览-推理服务数据
export const getMetricData = () => GET('/web/operation/serving/v1/metric/summary');

// 运营总览-资源概览-存储 | 镜像资源-镜像资源
export const getResourceQuotas = () => POST('/web/admin/image/v1/operator/manage/screen/get_resource_quotas');

// 运营总览-纳管资产-累计纳管镜像/现存纳管镜像
export const getImageMonthlyQoq = () => POST('/web/admin/image/v1/operator/manage/screen/image_monthly_qoq');

// 运营总览-存储镜像资源
export const getAllPoolUsage = () => GET('/web/admin/storage/v1/getAllPoolUsage');

// 运营总览-镜像资源
export const getHarborQuota = () => POST('/web/admin/image/v1/operator/manage/screen/get_harbor_quota');

// 运营总览-加速卡节点资源 & CPU节点资源
export const getResourceOverviewInfo = () => GET('/web/admin/resource/v1/platform/get-resource-overview-info');

// 运营总览-训练任务
export const getIndexStatistics = () => GET('/web/admin/task/v1/overview/indexStatistics');

// 运营总览-纳管资产
export const getMetricAllData = () => GET('/web/model/admin/v1/metric/summary/all');
