<template>
  <a-spin :spinning="loading" :wrapper-class-name="loading? 'app-loading-spin-box app-loading-spin': 'app-loading-spin'">
    <template #indicator>
      <RectLoading :size="size" />
    </template>
    <template #tip>
      <div class="tip-content">
        <h1 v-if="title" class="title">{{ title }}</h1>
        <p v-if="description" class="text">{{ description }}</p>
      </div>
    </template>
    <template v-for="(item, key, index) in $slots" :key="index" #[key]>
      <slot :name="key"></slot>
    </template>
  </a-spin>
</template>

<script setup>
import RectLoading from './RectLoadingIcon.vue';

const props = defineProps({
  loading: { // 是否开启加载
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: '',
  },
  description: {
    type: String,
    default: '',
  },
  size: { // loading尺寸
    type: String,
    default: 'small',
  },
});

</script>

<style lang="less" scoped>
:deep(.ant-spin .ant-spin-dot) {
  width: auto;
  height: auto;
}
// :deep(.ant-spin-container) {
//   height: 100%;
//   display: flex;
//   flex-direction: column;
//   justify-content: center;
// }
.app-loading-spin {
  flex: 1;
}
.app-loading-spin.app-loading-spin-box {
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.tip-content {
  padding-top: 20px;

}
.title {
  margin-bottom: 10px;
  font-weight: 400;
  font-size: 20px;
  // color: #00141a;
}
.text {
  font-weight: 400;
  font-size: 14px;
  // color: rgba(0, 20, 26, 0.7);
}
</style>
