import { MGT_RESGROUP_VIEW_AUTH, MGT_RESGROUP_EDIT_AUTH } from '@/constants/resourceGroup';

// 运管中心-资源组管理
const RESOURCE_GROUP = [
  {
    path: '/resource-group',
    name: 'resource-group',
    component: () => import('@/views/management-center/resource-group/index.vue'),
    meta: {
      header: [
        {
          name: '资源组管理',
        },
      ],
    },
    children: [
      //资源组管理列表
      {
        path: '',
        name: 'resource-group-list',
        component: () => import('@/views/management-center/resource-group/list/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '资源组管理',
            },
          ],
        },
      },
      //新建资源组
      {
        path: 'create',
        name: 'resource-group-create',
        component: () => import('@/views/management-center/resource-group/create/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '资源组管理',
              path: '/resource-group',
            },
            {
              name: '新建资源组',
            },
          ],
          apvAndMgtPermission: MGT_RESGROUP_EDIT_AUTH,
        },
      },
      //资源组详情
      {
        path: 'detail',
        name: 'resource-group-detail',
        component: () => import('@/views/management-center/resource-group/detail/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '资源组管理',
              path: '/resource-group',
            },
            {
              name: '资源组详情',
            },
          ],
          apvAndMgtPermission: MGT_RESGROUP_VIEW_AUTH,
        },
      },
      {
        path: 'detail/edit-node',
        name: 'resource-node-edit',
        component: () => import('@/views/management-center/resource-group/detail/edit/node.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '资源组管理',
              path: '/resource-group',
            },
            {
              name: '资源组详情',
              path: '/resource-group/detail',
              query: {
                tab: '节点信息',
                groupId: '',
              },
            },
            {
              name: '编辑节点',
            },
          ],
          apvAndMgtPermission: MGT_RESGROUP_EDIT_AUTH,
        },
      },
      //节点列表详情
      {
        path: 'node-detail',
        name: 'node-detail',
        component: () => import('@/views/management-center/resource-group/list/node-detail.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '资源组管理',
              path: '/resource-group',
              query: { tab: 2 },
            },
            {
              name: '节点详情',
              path: '/resource-group/node-detail',
              query: {
                tab: '节点信息',
              },
            },
            // {
            //   name: '节点信息',
            // },
          ],
          apvAndMgtPermission: MGT_RESGROUP_VIEW_AUTH,
        },
      },
      {
        path: 'detail/edit-related',
        name: 'resource-related-edit',
        component: () => import('@/views/management-center/resource-group/detail/edit/related.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '资源组管理',
              path: '/resource-group',
            },
            {
              name: '资源组详情',
              path: '/resource-group/detail',
              query: {
                tab: '关联项目',
                groupId: '',
              },
            },
            {
              name: '关联项目',
            },
          ],
          apvAndMgtPermission: MGT_RESGROUP_EDIT_AUTH,
        },
      },
    ],
  },
];

export default RESOURCE_GROUP;
