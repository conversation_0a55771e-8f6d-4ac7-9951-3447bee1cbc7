// 电话号码
export const telephoneNumberRegex = /^1[3456789]\d{9}$/;
// 6位验证码
export const vetifyCodeRegex = /^\d{6}$/;
// 中英文或空格
export const chineseOrLetterOrBlankRegex = /^[\u4e00-\u9fa5 a-zA-Z]+$/;
// 中英文
export const chineseOrLetterRegex = /^[\u4e00-\u9fa5a-zA-Z]+$/;
// 邮件
export const emailRegex = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
// 数字字母或下划线
export const numberOrLetterOrLineRegex = /^\w+$/;
// 学校格式的校验
export const schoolInputRegex = /^[\u4e00-\u9fa5a-zA-Z\s()（）]*$/;
// 30个字符以内，必须以中英文或数字开头，支持小括号、短横线和空格（模型训练处新建、新建增量预训练任务）
export const modelTrainCreateNameRegex = /^[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5\s\(\)\-\（\）]{0,29}$/;
// 保存镜像，镜像名称 24个字符内，以字母开头，支持英文小写a-z、数字0-9
export const imageNameRegex = /^[a-z][0-9a-z]{0,23}$/;
// 镜像导出，文件另存为（镜像名称） 24 个字符内，以字母开头,支持英文大小写 a-z、数字 0-9、下划线和短横线
export const exportImageNameRegex = /^[a-zA-Z][0-9A-Za-z\_\-]{0,23}$/;
// 保存镜像，标签 12个字符以内的大小写字母、数字、中划线、下划线、 小数点
export const imageTagRegex = /^[0-9a-zA-Z-_.]{0,12}$/;
// 模型管理-新建模型，模型名称校验 24个字符内，支持数字，英文大小写中划线及下划线
export const modelNameRegex = /^[0-9a-zA-Z-_]{0,24}$/;
// 模型管理-模型网络，模型网络名称校验 24个字符内，支持数字，英文大小写中划线及下划线、小数点
export const modelNetworkRegex = /^[0-9a-zA-Z-_.]{0,24}$/;
// 镜像管理，上传镜像文件名校验，文件名不能包含中文、空格、&和*
export const illegalImageFileNameRegex = /[*&\u4e00-\u9fa5\s]/;
// 镜像管理，上传镜像文件格式校验
export const imageFileSuffixRegex = /\.(tar|tgr|gz)$/i;
// 日志搜索关键词校验，关键词不能包含特殊正则表达式字符，“$, (), *, +, ., ?, , ^, {}, |”
export const logSearchWordRegex = /[$()*+.?\^{}|]/;
// 运管中心-存储目录管理-外部存储目录 必填，请按照ip:/path格式填写，数字或字母结尾，仅支持冒号:、中划线-、下划线_、小数点.、斜杠/五种字符
export const extStorageDirRegex = /^.+?:\/[\/\-.a-zA-Z0-9_]{0,995}[a-zA-Z0-9]$/;
// 运管中心-存储目录管理-外部存储目录 必填，请按照/path格式填写，数字或字母结尾，仅支持冒号:、中划线-、下划线_、小数点.、斜杠/五种字符
export const extStorageDirRegexPosix = /^\/[\/\-.a-zA-Z0-9_]{0,995}[a-zA-Z0-9]$/;
// 运管中心-存储目录管理-挂载至项目内部目录 必填，默认显示/root/work/externalstorage，拼接自定义内容，以/开头，数字或字母结尾，仅支持中划线-、下划线_、小数点.、斜杠/四种字符
export const projectInternalDir = /^\/[\/\-.a-zA-Z0-9_]{0,998}[a-zA-Z0-9]$/;
// 模型压缩任务名称校验，30个字符以内，支持中英文、数字、下划线_、中划线-和空格，以中英文开头
export const compressNameRegex = /^[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5\-_\s]{0,29}$/;
// 运管中心-预置资产管理-预制模型名称校验，50个字符以内，以字母开头，支持中英文大小写、数字、小数点、中下划线
export const presetModelNameRegex = /^[0-9\u4e00-\u9fa5a-zA-Z-_.]{0,49}$/;
// 运管中心-预置资产管理-新建预制模型-模型标签格式校验，24个字符，支持数字，英文大小写中划线及下划线
export const presetModelLabelRegex = /^[0-9\u4e00-\u9fa5a-zA-Z-_]{0,24}$/;
// 运管中心-预置资产管理-默认值，数字和小数点
export const presetModelNumberRegex = /^[0-9.-]{0,100}$/;
// 合法url
export const urlregex = /(https?|ftp|file):\/\/[-A-Za-z0-9+&@#/%?=~_|!:,.;]+[-A-Za-z0-9+&@#/%=~_|]/;
// 镜像分享，项目空间id校验，长度为32且必须同时包含数字和小写字母
export const projectSpaceIdRegex = /^(?=.*[a-z])(?=.*\d)[a-z0-9]{32}$/;
//评估指标  只能输入数字和英文逗号
export const indicatorRegex = /^[0-9,]{0,1000}$/;
// 预置模型训练参数规则
export const preModelTranParmasRegex = /^[-_a-zA-Z][-_a-zA-Z0-9]{0,1000}$/;
// @Iteration: [v2.0.1] 告警管理-通知组 > 30个字符以内，以字母开头，支持英文小写a-z、数字0-9、_和-
export const alarmNoticeNameRegex = /^[a-zA-Z][-_a-zA-Z0-9]{0,30}$/;
// 模型蒸馏，转存时数据集名称 30个字符以内，支持中文、英文大小写、数字、中英文小括号、下划线(_)与短横线(-)
export const kdDatasetNameRegex = /^[\w\u4e00-\u9fa5()\-_]{1,30}$/;
