import { GET, POST, requestWithPoolId } from '@/request';
const { GET: requestWithPoolIdGet, POST: requestWithPoolIdPost, requestBlob: requestWithPoolIdRequestBlob } = requestWithPoolId;

// 运管侧-操作日志接口
// 项目空间操作日志-操作模块
export const getModulesList = (data) => requestWithPoolIdGet('/web/admin/eventrack/v1/event-modules', data);
// 项目空间操作日志-列表
export const getEventLogs = (data) => requestWithPoolIdPost('/web/admin/eventrack/v1/event-logs', data);
// 项目空间操作日志-详情
export const getDetail = (data) => requestWithPoolIdGet('/web/admin/eventrack/v1/event-log/detail', data);
// 操作日志-获取项目空间
export const getProjectSpaceAllList = (data) => requestWithPoolIdGet('/web/admin/project/v1/project/list-all', data);
// 操作日志-导出
export const eventExport = (data) => requestWithPoolIdRequestBlob('/web/admin/eventrack/v1/event-log/export-event-log-detail', data, { responseType: 'blob' });
