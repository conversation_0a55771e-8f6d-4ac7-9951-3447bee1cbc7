import { getFormatTime } from '@/utils';

export const QUOTA_MANAGE_TABS = {
  EXPANSION: '1',
  QUOTA: '2',
  CREATE_PROJECT: '3',
  CREATE_QUOTA: '4'
};

export const QUOTA_MANAGE_EMPTY_TEXT = {
  [QUOTA_MANAGE_TABS.CREATE_PROJECT]: '用户新建项目申请工单',
  [QUOTA_MANAGE_TABS.CREATE_QUOTA]: '用户',
}

export const QUOTA_MANAGE_SEARCH_PLACEHOLDER = {
  [QUOTA_MANAGE_TABS.EXPANSION]: '请输入项目空间名称/申请人',
  [QUOTA_MANAGE_TABS.QUOTA]: '请输入项目空间名称/负责人',
  [QUOTA_MANAGE_TABS.CREATE_PROJECT]: '请输入需求/申请人/工作单位/处理人',
  [QUOTA_MANAGE_TABS.CREATE_QUOTA]: '请输入用户名/处理人',
}

export const GB_STORE_ARRAY = ['image', 'hs_stor', 'object_stor'];
export const TB_STORE_ARRAY = ['general_stor'];

export const DYNAMIC_DATA_MAP = {
  acce_card: {
    text: '加速卡',
    unit: '卡',
    order: 1,
  },
  cpu: {
    text: 'CPU',
    unit: '核',
    order: 2,
  },
  image: {
    text: '镜像资源',
    unit: 'GB',
    order: 3,
  },
  general_stor: {
    text: '普通文件存储',
    unit: 'GB',
    order: 4,
  },
  hs_stor: {
    text: '高性能文件存储',
    unit: 'GB',
    order: 5,
  },
  object_stor: {
    text: '对象存储',
    unit: 'GB',
    order: 6,
  },
};

export const NOTIFY_TEXT = {
  emailSendStatus: '邮件',
  smsSendStatus: '短信',
  messageSendStatus: '站内信',
};

export const APV_QUATO_VIEW_AUTH = { type: 'apv', key: 'quota', value: 'view' };
export const MGT_QUATO_VIEW_AUTH = { type: 'mgt', key: 'quota', value: 'view' };
export const APV_QUATO_EDIT_AUTH = { type: 'apv', key: 'quota', value: 'edit' };
export const MGT_QUATO_EDIT_AUTH = { type: 'mgt', key: 'quota', value: 'edit' };

export const HELP_TEXT_DEFAULT = '请输入非负整数';

// 调整配额允许最大值,最小值为用户当前使用的used
export const HIGH_LIMIT_QUOTA = 10000;

// 高性能存储单位
export const HS_STOR_UNIT_TPYE = {
  gb: 'GB',
  tb: 'TB',
};

// 普通文件存储
export const GENERAL_STOR_UNIT_TYPE = {
  gb: 'GB',
  tb: 'TB',
}

export const OBJECT_STOR_UNIT_TYPE = {
  gb: 'GB',
  tb: 'TB',
}

export const IMAGE_UNIT_TYPE = {
  gb: 'GB',
  tb: 'TB',
}

// 新建项目申请、新建项目配额管理对应的状态
export const ADMIN_PROJECT_QUOTA_STATUS = {
  PASS: 0, // 已通过
  PROCESSING: 1, // 待处理
  REVOKE: 2, // 已撤回
  REJECT: 3, // 已驳回
  INVALID: 4, // 已失效
}

export const ADMIN_PROJECT_QUOTA_TEXT = {
  [ADMIN_PROJECT_QUOTA_STATUS.PASS]: '已通过',
  [ADMIN_PROJECT_QUOTA_STATUS.PROCESSING]: '待处理',
  [ADMIN_PROJECT_QUOTA_STATUS.REVOKE]: '已撤回',
  [ADMIN_PROJECT_QUOTA_STATUS.REJECT]: '已驳回',
  [ADMIN_PROJECT_QUOTA_STATUS.INVALID]: '已失效',
}

export const ADMIN_PROJECT_QUOTA_COLOR = {
  [ADMIN_PROJECT_QUOTA_STATUS.PASS]: 'green',
  [ADMIN_PROJECT_QUOTA_STATUS.PROCESSING]: 'orange',
  [ADMIN_PROJECT_QUOTA_STATUS.REVOKE]: 'default',
  [ADMIN_PROJECT_QUOTA_STATUS.REJECT]: 'red',
  [ADMIN_PROJECT_QUOTA_STATUS.INVALID]: 'default',
}

export const createProjectColumn = [
  {
    title: '工单号',
    dataIndex: 'id',
    key: 'id',
    ellipsis: true,
  },
  {
    title: '需求名称',
    dataIndex: 'applyTitle',
    key: 'applyTitle',
    ellipsis: true,
  },
  {
    title: '需求描述',
    dataIndex: 'applyDesc',
    key: 'applyDesc',
    ellipsis: true,
  },
  {
    title: '申请人',
    dataIndex: 'applicant',
    key: 'applicant',
  },
  {
    /* （0:已处理 1:待处理 2:已撤回 3:已驳回 4:已失效） */
    title: '状态',
    dataIndex: 'status',
    key: 'status',
    filters: [
      {
        text: ADMIN_PROJECT_QUOTA_TEXT[ADMIN_PROJECT_QUOTA_STATUS.PROCESSING],
        value: ADMIN_PROJECT_QUOTA_STATUS.PROCESSING,
      }, {
        text: ADMIN_PROJECT_QUOTA_TEXT[ADMIN_PROJECT_QUOTA_STATUS.PASS],
        value: ADMIN_PROJECT_QUOTA_STATUS.PASS,
      }, {
        text: ADMIN_PROJECT_QUOTA_TEXT[ADMIN_PROJECT_QUOTA_STATUS.REJECT],
        value: ADMIN_PROJECT_QUOTA_STATUS.REJECT,
      }],
  },
  {
    title: '申请时间',
    dataIndex: 'applyTime',
    key: 'applyTime',
    sorter: true,
    customRender({ text }) {
      return text ? getFormatTime(text) : '--';
    },
    width: '180px',
  },
  {
    title: '处理时间',
    dataIndex: 'processTime',
    key: 'processTime',
    sorter: true,
    customRender({ text }) {
      return text ? getFormatTime(text) : '--';
    },
    width: '180px',
  },
  {
    title: '处理人',
    dataIndex: 'processor',
    key: 'processor',
    customRender({ text }) {
      return text || '--';
    },
  },
];
export const createQuotaColumn = [
  {
    title: '用户名',
    dataIndex: 'userName',
    key: 'userName',
  },
  {
    title: '新建项目配额',
    dataIndex: 'createProjectQuota',
    key: 'createProjectQuota',
    sorter: true,
  },
  {
    title: '已新建项目数',
    dataIndex: 'createProjectNum',
    key: 'createProjectNum',
    sorter: true,
  },
  {
    title: '配额更新时间',
    dataIndex: 'updateTime',
    key: 'updateTime',
    sorter: true,
    customRender({ text }) {
      return text ? getFormatTime(text) : '--';
    },
  },
  {
    title: '最后处理人',
    dataIndex: 'updateUser',
    key: 'updateUser',
    customRender({ text }) {
      return text || '--';
    },
  },
];

export const RESEND_MESSAGE_MAP = {
  130210: '短信发送过于频繁',
  130211: '邮件发送过于频繁',
  130212: '站内信发送过于频繁',
}

// 变更结果状态码
export const UPDATE_RESULT_CODE = {
  130214: 'CPU配额变更失败，服务器内部错误',
  130215: '加速卡配额变更失败，服务器内部错误',
  130216: '镜像配额变更失败，服务器内部错误',
  130217: '普通文件存储配额变更失败，服务器内部错误',
  140009: '普通文件存储配额变更失败，平台主目录容量不足',
  130218: '高性能文件存储配额变更失败，服务器内部错误',
  143006: '高性能文件存储配额变更失败，平台主目录容量不足',
  130219: '对象存储配额变更失败，服务器内部错误',
  // '0': '配额变更成功',
  // '': '配额变更成功',
}