import { GET, POST, requestWithPoolId } from '@/request';
const { GET: requestWithPoolIdGet, POST: requestWithPoolIdPost } = requestWithPoolId;

/* 
/用户相关指标
@Schema(description="入驻数（人）")
private int memberSettledNum;
@Schema(description="日活（人）：在截止日期前一天内的用户活跃人数")
private int userDayActiveCount;
@Schema(description="月活（人）：在截止日期前一月内的用户活跃人数")
private int userMonthActiveCount;
/项目相关指标
@Schema(description="创建数（个）")
private int projectCreatedNum;
@Schema(description="本月新增（个）")
private int projectNewlyAddedNum;
@Schema(description="日话（个）")
private int projectDayActiveCount;
@Schema(description="月活（个）")
private int projectMonthActiveCount;

*/
export const getProjectBoardApi = (data) => requestWithPoolIdGet('/web/admin/project/v1/stat/project-dashboard-info', data);

export const getProjectBoardResTableApi = (data) => requestWithPoolIdPost('/web/admin/project/v1/project/list-resource-detail-info', data);

/* 
获取项目看板-用户活跃趋势图数据/项目活跃趋势图数据
*/
export const getProjectActiveChartApi = (data) => requestWithPoolIdGet('/web/admin/project/v1/stat/list-active-summary', data);

/* 
运管中心-活动看板-指标统计 顶部卡片 训练任务数据
*/
export const getActiveTopTaskIndexStatisticsApi = (data) => requestWithPoolIdGet('/web/admin/task/v1/indexStatistics', data);

/* 
训练任务 时间趋势 曲线图 
{
  "beginTimestamp": 0,
  "endTimestamp": 0,
  "type": "month"
}

*/
export const getActiveTaskChartLineApi = (data) => requestWithPoolIdPost('/web/admin/task/v1/summary/trend', data);

/* 
训练任务 柱图 
{
  "beginTimestamp": 0,
  "endTimestamp": 0,
  "type": "resourceGroup"
}

*/
export const getActiveTaskChartBarApi = (data) => requestWithPoolIdPost('/web/admin/task/v1/summary/property', data);

/* 
运管中心-活动看板-指标统计 顶部卡片 推理服务
*/
export const getActiveTopReasonApi = (data) => requestWithPoolIdGet('/web/admin/serving/v1/metric/summary', data);

/* 
活动看板 - 推理服务 曲线图
{
  "beginTimestamp": 0,
  "endTimestamp": 0,
  "type": "month"
}

*/
export const getActiveReasonChartLineApi = (data) => requestWithPoolIdPost('/web/admin/serving/v1/summary/trend', data);

/* 
推理服务 柱图 
{
  "beginTimestamp": 0,
  "endTimestamp": 0,
  "type": "resourceGroup"
}

*/
export const getActiveReasonChartBarApi = (data) => requestWithPoolIdPost('/web/admin/serving/v1/summary/property', data);

/* 
运管中心-活动看板-指标统计 顶部卡片  开发环境
*/
export const getActiveTopDevelopIndexStatisticsApi = (data) => requestWithPoolIdGet('/web/admin/develop/v1/indexStatistics', data);

/* 
活动看板 - 推理服务 曲线图
{
  "beginTimestamp": 0,
  "endTimestamp": 0,
  "type": "month"
}

*/
export const getActiveDevelopChartLineApi = (data) => requestWithPoolIdPost('/web/admin/develop/v1/trend', data);

/* 
推理服务 柱图 
{
  "beginTimestamp": 0,
  "endTimestamp": 0,
  "type": "resourceGroup"
}

*/
export const getActiveDevelopChartBarApi = (data) => requestWithPoolIdPost('/web/admin/develop/v1/attribute', data);

/* 
资源看板 - 资源概览 左边的饼图的接口
*/
export const getResChartPieApi = (data) => requestWithPoolIdGet('/web/admin/storage/v1/getStoragePercentInfo', data);

/* 
资源看板 - 资源概览 右边的table
*/
export const getResTableApi = (data) => requestWithPoolIdGet('/web/admin/storage/v1/getProjectUsagePercent', data);

/* 
1: 获取加速卡类型
*/
export const getGpuTypeApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/gpu-card-type', data);

/* 
2. 获取集群信息
*/
export const getClusterApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/cluster-info', data);

/* 
1: 资源看板-资源组概览-资源组列表服务接口
*/
export const getResViewTableApi = (data) => requestWithPoolIdPost('/web/admin/resource/v1/board/get-group-overview-list', data);

/* 
运管中心-活动看板-指标统计 顶部卡片  开发环境
*/
export const getActiveTopModelIndexStatisticsApi = (data) => requestWithPoolIdGet('/web/model/admin/v1/metric/summary', data);

/* 
运管中心-资源看板 资源组列表
*/
export const getActiveAllResGroupApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/board/get-pool-all-resource-group-list', data);

/* 
8、运营看板-资源看板-节点概览-节点下拉列表
*/
export const getActiveAllNodeApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/board/get-node-list', data);

/* 
6、运营看板-资源看板-节点概览-运行实例水位图
*/
export const getPodChartLineApi = (data) => requestWithPoolIdPost('/web/admin/resource/v1/board/get-pod-graph', data);

/* 
5、运营看板-资源看板-节点概览-资源占用量水位图
*/
export const getNodeChartLineApi = (data) => requestWithPoolIdPost('/web/admin/resource/v1/board/get-resource-graph', data);

/* 
4、运营看板-资源看板-节点概览-资源占用率水位图
*/
export const getNodeChartLineRateApi = (data) => requestWithPoolIdPost('/web/admin/resource/v1/board/get-resource-rate-graph', data);

/* 
  运营看板-资源看板-节点概览-资源使用率水位图
*/
export const getUseRateApi = (data) => requestWithPoolIdPost('/web/admin/resource/v1/board/get-resource-use-rate-graph', data);

/* 
运管中心-活动看板-指标统计 顶部卡片  纳管镜像
*/
export const getActiveTopImageApi = (data) => requestWithPoolIdPost('/web/admin/image/v1/operator/manage/image_monthly_qoq', data);
/* 
运管中心-活动看板 服务调用量趋势图：
*/

export const getServingChartLineApi = (data) => requestWithPoolIdPost('/web/admin/serving/v1/call/trend', data);

/* 
运管中心-活动看板 服务调用TOP10
*/

export const getServingChartBarsApi = (data) => requestWithPoolIdPost('/web/admin/serving/v1/call/topk', data);

/* 
运管中心-活动看板
调用详情：
*/
export const getServingTableApi = (data) => requestWithPoolIdPost('/web/admin/serving/v1/call/detail', data);

// 获取镜像资源列表
export const getImageProjectTable = (data) => requestWithPoolIdGet('/web/admin/image/v1/operator/manage/image_projects_quota', data);

// 项目空间镜像资源占用率
export const getImageRateStatus = (data) => requestWithPoolIdPost('/web/admin/image/v1/operator/manage/image_quota_rateStatus', data);

// 体验看板-获取所有模型列表
export const getModelList = () => requestWithPoolIdGet('/web/admin/experience/v1/overview/service');

//体验看板-获取项目空间列表
export const getProjectSpaceist = () => requestWithPoolIdGet('/web/admin/project/v1/project/list-all');

//体验看板-获取会话列表
export const getChatList = (data) => requestWithPoolIdPost('/web/admin/experience/v1/overview/list', data);

//体验看板-模型热度TOP5/全部模型的热度与评价情况
export const getHotList = (data) => requestWithPoolIdGet('/web/admin/experience/v1/overview/hot', data);

//体验看板-会话详情-基本信息及评价分布
export const getDetail = (data) => requestWithPoolIdGet('/web/admin/experience/v1/overview/detail', data);

//体验看板-会话详情-对话记录
export const getDetailRecord = (data) => requestWithPoolIdGet('/web/admin/experience/v1/overview/record', data);

//体验看板-会话详情-单轮会话配置参数
export const getDetailParam = (data) => requestWithPoolIdGet('/web/admin/experience/v1/overview/param', data);

// 用户访问记录
export const recordUserVisit = () => requestWithPoolIdGet('/web/project/v1/member/visit');

/* 
  获取项目看板-项目资源使用和项目活动资源使用列表、导出
*/
export const getProjectUsageApi = (data) => requestWithPoolIdPost('/web/admin/project/v1/stat/list-project-usage', data);
export const getProjectUsageExport = (data) => requestWithPoolIdPost('/web/admin/project/v1/stat/project-usage-export', data);
export const getProjectActUsageApi = (data) => requestWithPoolIdPost('/web/admin/project/v1/stat/list-project-act-usage', data);
export const getProjectActUsageExport = (data) => requestWithPoolIdPost('/web/admin/project/v1/stat/project-act-usage-export', data);
// 获取项目活动
export const getProjectActType = (data) => requestWithPoolIdGet('/web/resource/v1/project-act-types', data);
