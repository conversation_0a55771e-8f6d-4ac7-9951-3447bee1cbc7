import { addGetKeyMethod } from '@/utils';

export const SECNE_MAP = {
  ALL: '0', // 训练+调优+推理+数据
  TRAIN: '1', // 训练
  SERVICE: '2', // 推理
  OPTIMIZATION: '3', // 调优
  DATA: '4', //数据
};
export const SCENE_TYPE = {
  [SECNE_MAP.ALL]: '数据，训练,调优,推理',
  [SECNE_MAP.TRAIN]: '训练',
  [SECNE_MAP.SERVICE]: '推理',
  [SECNE_MAP.DATA]: '数据',
};

// 选择的资源组卡类型
export const RESOURCE_TYPE_ENUM_OBJ = {
  CPU: 'cpu', // cpu
  GPU_CARD: 'gpucard', // 加速卡，记得以后会添加vgpu
};

// 资源组类型，cpu或者加速卡
export const RESOURCE_TYPE = {
  cpu: 'CPU',
  gpucard: '加速卡',
};

export const NODE_STATUS = {
  1: '可用',
  0: '不可用',
  2: '禁止调度',
};

export const RESOURCE_ATTR_KEY = {
  PUBLIC: 'public',
  PRIVATE: 'private',
};

export const RESOURCE_ATTRIBUTE = {
  [RESOURCE_ATTR_KEY.PUBLIC]: '公共资源组',
  [RESOURCE_ATTR_KEY.PRIVATE]: '专属资源组',
};

addGetKeyMethod(SCENE_TYPE);

export const MGT_RESGROUP_EDIT_AUTH = { type: 'mgt', key: 'resgroup', value: 'edit' };
export const MGT_RESGROUP_VIEW_AUTH = { type: 'mgt', key: 'resgroup', value: 'view' };

export const CURRENT_TAB_MAP = {
  RESOURCE: '资源看板',
  NODE: '节点信息',
  PROJECT: '关联项目',
};
