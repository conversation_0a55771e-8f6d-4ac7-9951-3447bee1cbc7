import { requestWithProjectId } from '@/request';
const { GET: requestWithProjectIdGET, POST: requestWithProjectIdPOST } = requestWithProjectId;

// 数据回流列表
// 获取数据回流任务列表
export const getDataRefluxTaskList = (data) => requestWithProjectIdPOST('/web/serving/v1/reflux/task/list', data);
// 删除回流服务
export const delReflux = (data) => {
  const { taskId } = data;
  return requestWithProjectIdPOST(`/web/serving/v1/reflux/task/delete?taskId=${taskId}`);
};

// 新建回流
// 校验任务名称是否重复
export const checkRefluxNameExit = (data) => requestWithProjectIdGET('/web/serving/v1/reflux/task/duplicate', data);
// 搜索服务名称下拉选项
export const searchInstanceName = (data) => requestWithProjectIdGET('/web/serving/v1/reflux/task/instance/list', data);
// 提交表单
export const submitReflux = (data) => requestWithProjectIdPOST('/web/serving/v1/reflux/task/create', data);

// 数据回流详情页
// 获取数据详情
export const getRefluxDetail = (data) => requestWithProjectIdGET('/web/serving/v1/reflux/task/detail', data);
// 查询调用统计列表
export const getCallStatisTableList = (url, data) => requestWithProjectIdGET(url, data);
// 删除调用统计数据
export const delCallStatis = (url, data) => requestWithProjectIdPOST(url, data);
// 保存-弹窗-校验数据集名称是否重复
export const checkDataSetNameExit = (data) => requestWithProjectIdPOST('/web/dataset/v1/external/queryDataset', data);
// 保存-弹窗-获取数据集的数据
export const getDataSetPath = (url, data) => requestWithProjectIdPOST(url, data);
// 保存-弹窗-传输数据给数据集
export const saveDataSet = (data) => requestWithProjectIdPOST('/web/dataset/v1/init/version', data);
// 保存-弹窗-非流模式传输数据给数据集
export const syncStreamVersion = (data) => requestWithProjectIdPOST('/web/dataset/v1/reflux/syncStreamVersion', data);
