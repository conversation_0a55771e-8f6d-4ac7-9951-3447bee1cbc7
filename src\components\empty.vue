<template>
  <div class="empty-container">
    <div v-if="showOperation" class="empty-content" :style="{ backgroundImage: 'url(' + emptyImage + ')' }">
      <p v-if="title" class="title">目前暂无{{ title }}</p>
      <p v-else class="title">
        <slot name="title"></slot>
      </p>
      <p class="description">
        <slot name="description"></slot>
      </p>
    </div>
    <div v-else class="empty-content" :style="{ backgroundImage: 'url(' + sousuokongImage + ')' }">
      <p v-if="title" class="title">抱歉，没有找到相关{{ title }}</p>
      <p v-else class="title">
        <slot name="title"></slot>
      </p>
      <p class="description">您可以换一个关键词试试</p>
    </div>
  </div>
</template>

<script>
const emptyImage = require('@/assets/images/empty/no_content.png');
const sousuokongImage = require('@/assets/images/empty/search_empty.png');
export default {
  props: {
    showOperation: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    emptyImage: {
      type: [Object, String],
      default() {
        return emptyImage;
      },
    },
    sousuokongImage: {
      type: [Object, String],
      default() {
        return sousuokongImage;
      },
    },
  },
};
</script>
<style lang="less" scoped>
.empty-container {
  display: flex;
  justify-content: center;
  .empty-content {
    width: 416px;
    height: 416px;
    padding-top: 270px;
    background-image: url(/src/assets/images/empty/search_empty.png);
    background-size: 100% auto;
    background-repeat: no-repeat;
    text-align: center;
    margin-top: 70px;
    .title {
      font-weight: 400;
      font-size: 16px;
      color: #121f2c;
      margin-bottom: 12px;
    }
    .description {
      font-weight: 400;
      font-size: 14px;
      color: #555555;
      /deep/.ant-btn {
        padding-right: 0;
        padding-left: 4px;
      }
    }
  }
}
.primary-color-link {
  margin-left: 4px;
  color: @jt-primary-color;
}
</style>
