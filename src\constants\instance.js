// 状态对照表
export const STATUS = {
  onSTART: 1, // 启动中
  RUNNING: 3, // 运行中
  onSTOP: 4, // 停止中
  STOP: 5, // 已停止
  SUCCESS: 0, //成功
  FAIL: 6, // 失败
  SNAPSHOT: 7, // 快照中
};

export const getStatusMarkStyle = (status) => {
  const statusClassnamemap = new Map([
    [STATUS.onSTART, 'warning'],
    [STATUS.onSTOP, 'warning'],
    [STATUS.SNAPSHOT, 'warning'],
    [STATUS.RUNNING, 'processing'],
    [STATUS.FAIL, 'error'],
    [STATUS.SUCCESS, 'complete'],
    [STATUS.STOP, 'default'],
  ]);
  if (!statusClassnamemap.has(+status)) {
    return +status;
  }
  return statusClassnamemap.get(+status);
};
export const getStatusMarkWord = (status) => {
  const statusWordmap = new Map([
    [STATUS.onSTART, '启动中'],
    [STATUS.onSTOP, '停止中'],
    [STATUS.RUNNING, '运行中'],
    [STATUS.FAIL, '失败'],
    [STATUS.SUCCESS, '成功'],
    [STATUS.STOP, '已停止'],
    [STATUS.SNAPSHOT, '快照中'],
  ]);
  if (!statusWordmap.has(+status)) {
    return +status;
  }
  return statusWordmap.get(+status);
};
