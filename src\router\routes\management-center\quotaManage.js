import { APV_QUATO_VIEW_AUTH, MGT_QUATO_VIEW_AUTH, QUOTA_MANAGE_TABS } from '@/constants/quota';

// 运管中心-配额管理
const QUOTA_MANAGE = [
  {
    path: '/quota-manage',
    name: 'quota-manage',
    component: () => import('@/views/management-center/quota-manage/index.vue'),
    meta: {
      header: [
        {
          name: '配额管理',
        },
      ],
    },
    children: [
      {
        path: '',
        name: 'quota',
        component: () => import('@/views/management-center/quota-manage/quota/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '配额管理',
            },
          ],
        },
      },
    ],
  },
  // 扩容工单详情
  {
    path: '/quota-manage/quota/expansion/detail/:id',
    name: 'expansion-detail',
    component: () => import('@/views/management-center/quota-manage/quota/expansion-detail/index.vue'),
    meta: {
      header: [
        {
          name: '运管中心',
        },
        {
          name: '配额管理',
          path: '/quota-manage',
          query: {
            tab: 1,
          },
        },
        {
          name: `扩容工单详情`,
        },
      ],
      apvAndMgtPermission: APV_QUATO_VIEW_AUTH,
    },
  },
  // 配额详情
  {
    path: '/quota-manage/quota/detail/:id',
    name: 'quota-detail',
    component: () => import('@/views/management-center/quota-manage/quota/quota-detail/index.vue'),
    meta: {
      header: [
        {
          name: '运管中心',
        },
        {
          name: '配额管理',
          path: '/quota-manage',
          query: {
            tab: 2,
          },
        },
        {
          name: '项目空间配额详情',
        },
      ],
      apvAndMgtPermission: MGT_QUATO_VIEW_AUTH,
    },
  },

  /* 新建项目申请详情 */
  {
    path: '/quota-manage/create-project/detail/:id',
    name: 'create-project-detail',
    component: () => import('@/views/management-center/quota-manage/quota/create-detail/index.vue'),
    meta: {
      type: QUOTA_MANAGE_TABS.CREATE_PROJECT,
      header: [
        {
          name: '运管中心',
        },
        {
          name: '配额管理',
          path: '/quota-manage',
        },
        {
          name: '用户新建项目申请详情',
        },
      ],
      apvAndMgtPermission: APV_QUATO_VIEW_AUTH,
    },
  },
  /* 新建项目配额申请详情 */
  {
    path: '/quota-manage/create-quota/detail/:id',
    name: 'create-quota-detail',
    component: () => import('@/views/management-center/quota-manage/quota/create-detail/index.vue'),
    meta: {
      type: QUOTA_MANAGE_TABS.CREATE_QUOTA,
      header: [
        {
          name: '运管中心',
        },
        {
          name: '配额管理',
          path: '/quota-manage',
        },
        {
          name: '用户新建项目配额详情',
        },
      ],
      apvAndMgtPermission: MGT_QUATO_VIEW_AUTH,
    },
  },
];

export default QUOTA_MANAGE;
