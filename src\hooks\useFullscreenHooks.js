import { ref, onMounted, onUnmounted } from 'vue';
import screenful from 'screenfull';
function useFullscreenHooks() {
  const isFullscreen = ref(screenful.isFullscreen ? true : false);
  const screenFulIconType = ref(screenful.isFullscreen ? 'icontuichuquanping' : 'iconquanping');

  onMounted(() => {
    screenful.on('change', () => {
      isFullscreen.value = screenful.isFullscreen;
      screenFulIconType.value = screenful.isFullscreen ? 'icontuichuquanping' : 'iconquanping';
    });
  });

  onUnmounted(() => {
    screenful.off('change');
  });
  return {
    isFullscreen,
    screenFulIconType,
  };
}

export default useFullscreenHooks;
