import { presetModelApi } from '@/apis';
import { getEnvConfig } from '@/config';

export const showDatasetShare = computed(() => {
  return getEnvConfig('SHOW_DATASET_SHARE') === '1';
});

export const datasetDrawerBodyStyle = {
  paddingTop: '9px',
  paddingBottom: '0',
};
export const datasetTableScroll = { y: 'calc(100vh - 56px - 60px - 9px - 47px - 80px - 48px)' };
// 列表参数对应文案
export const DATASET_TEXT = {
  // 数据集分类
  classification: {
    // 0: '文本',
    // 1: '图像',
    // 2: '音频',
    // 3: '视频',
    // 4: '多模态',
    // 5: '其他',
  },
  // 数据类型
  datasetType: {
    // 0: '问答对',
    // 1: '纯文本',
    // 2: '图文问答对',
  },
  // 数据状态（默认：1）
  status: {
    0: '已删除',
    1: '未删除',
  },
  // 数据来源
  dataSource: {
    0: '对象存储导入',
    1: '文件存储导入',
    2: '本地上传',
  },
  // 上传状态
  uploadStatus: {
    0: '导入中',
    1: '已暂停',
    2: '导入失败',
    3: '导入完成',
  },
  // 数据页面
  dataPage: {
    0: '正在导入',
    1: '版本列表',
  },
  // 数据集版本是否可删除
  versionDelete: {
    0: '可删除',
    1: '不可删除',
  },
};
// 获取最新文案
presetModelApi.getDataSetType().then((res) => {
  if (res.code === 0) {
    DATASET_TEXT.datasetType = res.data.datasetType;
    DATASET_TEXT.classification = res.data.classification;
  }
});

// 自定的数据集类型，用于区分是哪种数据集，以便于删除
export const DATASET_TYPES = (() => {
  const types = {
    CUSTOM: 'custom', // 自定义
    PRESET: 'preset', // 预置数据集
  };
  if (showDatasetShare.value) {
    types.SHARE = 'share'; // 分享
  }
  return types;
})();

// 自定的数据集类型对应文案和tag颜色
export const DATASET_ATTR = {
  [DATASET_TYPES.CUSTOM]: {
    color: 'orange',
    text: '自定义数据集',
  },
  [DATASET_TYPES.PRESET]: {
    color: 'blue',
    text: '预置数据集',
  },
  [DATASET_TYPES.SHARE]: {
    color: 'tag-vscode-jt',
    text: '被分享数据集',
  },
};
