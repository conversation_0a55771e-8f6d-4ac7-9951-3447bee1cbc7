import dayjs from 'dayjs';
import { resourceApi } from '@/apis';
import { modelTaskApi } from '@/apis';
import { STATUS_TEXT_MAP, INSTATUS_TEXT_MAP } from '@/constants/index';
import { changeUseTime } from '@/constants/trainTask';

// 任务类型枚举
export const OPTIMIZE_TASK_TYPE = {
  INC_PRETRAIN: 'inc-pretrain',
  SFT: 'sft',
  DPO: 'dpo',
  KD: 'kd',
};

// 任务类型对应的新建页面路径
export const OPTIMIZE_TASK_CREATE_PATH = {
  'inc-pretrain': '/incremental-pretrain',
  sft: '/supervised-finetune',
  dpo: '/preference-alignment',
  kd: '/model-distillation',
};

export const OPTIMIZE_TASK_TYPE_MSG = {
  [OPTIMIZE_TASK_TYPE.INC_PRETRAIN]: '增量预训练',
  [OPTIMIZE_TASK_TYPE.SFT]: '有监督微调',
  [OPTIMIZE_TASK_TYPE.DPO]: '偏好对齐',
  [OPTIMIZE_TASK_TYPE.KD]: '模型蒸馏',
};

// 增量预训练&有监督微调状态表
export const OPTIMIZE_STATUS = {
  onSTART: 1, // 启动中
  RUNNING: 3, // 运行中
  onSTOP: 5, // 停止中
  STOP: 7, // 已停止
  FAIL: 6, // 失败
  QUEUE: 2, // 排队中
  STARTED: 4, //已启动、成功
};

// 资源组大类（公共资源组、专属资源组）
export const RESOURCE_GROUP_TYPE = {
  PUBLIC: 'public', // 公共资源组
  PERSONAL: 'personal', // 专属资源组
};

export const getOptimizeTaskStatusMarkWord = (type, status) => {
  const statusWordmap = new Map([
    [OPTIMIZE_STATUS.QUEUE, STATUS_TEXT_MAP.QUEUE],
    [OPTIMIZE_STATUS.onSTART, STATUS_TEXT_MAP.STARTING],
    [OPTIMIZE_STATUS.onSTOP, STATUS_TEXT_MAP.STOPPING],
    [OPTIMIZE_STATUS.RUNNING, STATUS_TEXT_MAP.RUNNING],
    [OPTIMIZE_STATUS.FAIL, STATUS_TEXT_MAP.FAIL],
    [OPTIMIZE_STATUS.STOP, STATUS_TEXT_MAP.STOPPED],
    [OPTIMIZE_STATUS.STARTED, STATUS_TEXT_MAP.SUCCESS],
  ]);
  const getStatus = isNaN(+status) ? status : +status;
  if (!statusWordmap.has(getStatus)) {
    return getStatus;
  }
  return statusWordmap.get(getStatus);
};

export function optimizeTaskBtnDisabled(type, record) {
  let disabled = false;
  const status = isNaN(+record.status) ? +record.state : +record.status;
  // 训练成功是否已超过30天
  const endTimeStamp = dayjs(record?.endTime).valueOf();
  const last30Days = dayjs(dayjs().valueOf()).subtract(30, 'day').valueOf(); //距离现在30天前的时间戳
  switch (type) {
    case 'run':
      disabled = [OPTIMIZE_STATUS.onSTART].includes(status);
      break;
    case 'stop':
      disabled = [OPTIMIZE_STATUS.onSTOP, OPTIMIZE_STATUS.STARTED].includes(status);
      break;
    case 'copy':
      disabled = false;
      break;
    case 'delete':
      disabled = false;
      break;
    case 'transfer':
      // show = [OPTIMIZE_STATUS.FAIL, OPTIMIZE_STATUS.STOP, OPTIMIZE_STATUS.STARTED].includes(status);
      disabled = endTimeStamp < last30Days;
      break;
    case 'publish':
      // 若任务训练成功已超过30天则不可再发布
      disabled = endTimeStamp < last30Days;
      break;
  }
  return disabled;
}

export function showOptimizeTaskBtn(type, record, pageType) {
  let show = false;
  const status = isNaN(+record.status) ? +record.state : +record.status;
  switch (type) {
    case 'run':
      show = [OPTIMIZE_STATUS.STOP, OPTIMIZE_STATUS.FAIL, OPTIMIZE_STATUS.onSTART].includes(status);
      break;
    case 'stop':
      // KUNLUN-7705: 发布按钮，是占停止按钮的位置的（即有发布按钮时不显示置灰的停止按钮）， 使操作列一直是4个操作项，不会有5个  --- 针对模型蒸馏
      if (pageType === OPTIMIZE_TASK_TYPE.KD) {
        show = [OPTIMIZE_STATUS.RUNNING, OPTIMIZE_STATUS.onSTOP, OPTIMIZE_STATUS.QUEUE, OPTIMIZE_STATUS.STARTED].includes(status) && !(optimizeTaskBtnDisabled(type, record) && showOptimizeTaskBtn('publish', record));
      } else {
        show = [OPTIMIZE_STATUS.RUNNING, OPTIMIZE_STATUS.onSTOP, OPTIMIZE_STATUS.QUEUE, OPTIMIZE_STATUS.STARTED].includes(status);
      }
      break;
    case 'copy':
      show = true;
      break;
    case 'delete':
      show = true;
      break;
    case 'transfer': // 成功、失败、已停止、且后台已完成蒸馏数据
      show = record.canResave;
      break;
    case 'publish':
      show = [OPTIMIZE_STATUS.STARTED].includes(status);
      break;
  }
  return show;
}

/**
 * 列表的启动时长变更
 * @param {array} list 变更列表
 */
export const changeTableListUseTime = (list) => {
  for (const i in list) {
    const item = list[i];
    if (item.status == OPTIMIZE_STATUS.RUNNING) {
      list[i].useTime = changeUseTime(item.useTime);
    }
  }
  return new Promise((resolve) => {
    resolve(list);
  });
};

// 所属资源组
export const getResourceGroupList = async () => {
  let resourceGroupList = [];
  try {
    const publicResourceGroupList = await resourceApi.getListAllResourceGroup();
    if (publicResourceGroupList.code === 0) {
      resourceGroupList = resourceGroupList.concat(publicResourceGroupList.data);
    }
  } catch (err) {
    throw new Error(err);
  }
  return Promise.resolve(resourceGroupList);
};

// 预训练基础模型
export const getBaseModelList = async () => {
  let preBaseModelList = [];
  try {
    const publicpreBaseModelList = await modelTaskApi.getPretrainList();
    if (publicpreBaseModelList.code === 0) {
      preBaseModelList = preBaseModelList.concat(publicpreBaseModelList.data);
    }
  } catch (err) {
    throw new Error(err);
  }
  return Promise.resolve(preBaseModelList);
};
// 微调基础模型
export const getSftModelList = async () => {
  let sftBaseModelList = [];
  try {
    const publicsftBaseModelList = await modelTaskApi.getSftList();
    if (publicsftBaseModelList.code === 0) {
      sftBaseModelList = sftBaseModelList.concat(publicsftBaseModelList.data);
    }
  } catch (err) {
    throw new Error(err);
  }
  return Promise.resolve(sftBaseModelList);
};
// 偏好基础模型
export const getDpoModelList = async () => {
  let dpoBaseModelList = [];
  try {
    const publicDpoBaseModelList = await modelTaskApi.getDpoList();
    if (publicDpoBaseModelList.code === 0) {
      dpoBaseModelList = dpoBaseModelList.concat(publicDpoBaseModelList.data);
    }
  } catch (err) {
    throw new Error(err);
  }
  return Promise.resolve(dpoBaseModelList);
};
// 获取教师模型
export const getKDTeacherModelList = async (mode) => {
  let teacherModelList = [];
  try {
    const res = await resourceApi.getTeacherModelList(mode);
    if (res.code === 0) {
      teacherModelList = res.data;
    }
  } catch (error) {
    throw new Error(err);
  }
  return Promise.resolve(teacherModelList);
};

export function modelOptimizeApiInfo(type) {
  return {
    start: `/web/finetune/v1/${type}/task/start`, // 运行增量预训练/有监督微调/偏好对齐任务
    create: `/web/finetune/v1/${type}/task/create`, // 新建增量预训练/有监督微调/偏好对齐任务
    getParamList: `/web/finetune/v1/${type}/param/list`, // 获取模型微调参数配置
    publish: `/web/finetune/v1/${type}/model/publish`, // 发布模型
    getCheckpointList: `/web/finetune/v1/${type}/model/checkpoint_list`, //
    stop: `/web/finetune/v1/${type}/task/stop`, // 停止增量预训练/有监督微调/偏好对齐任务
    delete: `/web/finetune/v1/${type}/task/delete`, // 删除增量预训练/有监督微调/偏好对齐任务
    getLogs: `/web/finetune/v1/${type}/task/log`, // 增量预训练/有监督微调/偏好对齐训练日志
    list: `/web/finetune/v1/${type}/task/list`, // 增量预训练/有监督微调/偏好对齐列表查询
    evaluation: `/web/finetune/v1/${type}/task/evaluation`, // 增量预训练/有监督微调/偏好对齐指标评估
    detail: `/web/finetune/v1/${type}/task/detail`, // 增量预训练/有监督微调/偏好对齐详情
    dataList: `/web/finetune/v1/${type}/task/dataset/list`, // 分页查询任务数据集
    batchDelete: `/web/finetune/v1/${type}/task/batch/delete`, // 批量删除
  };
}
//自定义筛选
export const tableStatusFilter = (type) => {
  if (type === OPTIMIZE_TASK_TYPE.INC_PRETRAIN) {
    return [
      // 运行中
      {
        text: INSTATUS_TEXT_MAP.RUNNING,
        value: OPTIMIZE_STATUS.RUNNING,
      },
      //启动中
      {
        text: INSTATUS_TEXT_MAP.STARTING,
        value: OPTIMIZE_STATUS.onSTART,
      },
      //排队中
      {
        text: INSTATUS_TEXT_MAP.WAITING,
        value: OPTIMIZE_STATUS.QUEUE,
      },
      //停止中
      {
        text: INSTATUS_TEXT_MAP.STOPPING,
        value: OPTIMIZE_STATUS.onSTOP,
      },
      // 已停止
      {
        text: INSTATUS_TEXT_MAP.STOPPED,
        value: OPTIMIZE_STATUS.STOP,
      },
      //成功
      {
        text: INSTATUS_TEXT_MAP.SUCCEED,
        value: OPTIMIZE_STATUS.STARTED,
      },
      //失败
      {
        text: INSTATUS_TEXT_MAP.FAILED,
        value: OPTIMIZE_STATUS.FAIL,
      },
    ];
  }
  if (type === OPTIMIZE_TASK_TYPE.SFT) {
    return [
      // 运行中
      {
        text: INSTATUS_TEXT_MAP.RUNNING,
        value: OPTIMIZE_STATUS.RUNNING,
      },
      //启动中
      {
        text: INSTATUS_TEXT_MAP.STARTING,
        value: OPTIMIZE_STATUS.onSTART,
      },
      //排队中
      {
        text: INSTATUS_TEXT_MAP.WAITING,
        value: OPTIMIZE_STATUS.QUEUE,
      },
      //停止中
      {
        text: INSTATUS_TEXT_MAP.STOPPING,
        value: OPTIMIZE_STATUS.onSTOP,
      },
      // 已停止
      {
        text: INSTATUS_TEXT_MAP.STOPPED,
        value: OPTIMIZE_STATUS.STOP,
      },
      //失败
      {
        text: INSTATUS_TEXT_MAP.FAILED,
        value: OPTIMIZE_STATUS.FAIL,
      },
      //成功
      {
        text: INSTATUS_TEXT_MAP.SUCCEED,
        value: OPTIMIZE_STATUS.STARTED,
      },
    ];
  }
};

export const tagColor = (status) => {
  let color = '';
  const statusColorMap = {
    运行中: 'green',
    启动中: 'orange',
    停止中: 'orange',
    已停止: 'default',
    成功: 'cyan',
    失败: 'red',
    重试中: 'red',
    排队中: 'quete-yellow-jt',
    快照中: 'orange',
    已锁定: 'default',
    部分运行中: 'green',
  };
  if (status && statusColorMap[status]) {
    color = statusColorMap[status];
  }
  return color;
};

// 增量预训练&有监督微调&偏好对齐，需要特殊处理的错误码（需要读取接口返回的错误信息）
export const SPECIAL_ERR_CODE = [240017, 240027, 240018, 240026, 240028, 240030, 240035, 240036, 240054, 240055, 240059];

// 特殊提示错误码，且是中间toast提示
export const SPECIAL_ERROR_CODE_TOAST = [240045];
//空态提示文案
export const OPTIMIZE_TASK_TYPE_EMPTY_MSG = {
  [OPTIMIZE_TASK_TYPE.INC_PRETRAIN]: '增量预训练任务',
  [OPTIMIZE_TASK_TYPE.SFT]: '有监督微调任务',
  [OPTIMIZE_TASK_TYPE.DPO]: '偏好对齐任务',
  [OPTIMIZE_TASK_TYPE.KD]: '模型蒸馏任务',
};
