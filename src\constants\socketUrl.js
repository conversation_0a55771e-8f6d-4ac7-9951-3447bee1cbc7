import { getWebSocketProtocolAndDomain } from '@/utils';

// const envPrefix = process.env.NODE_ENV === 'development' ? '/dev_websocket' : '';

// const socketPrefix = getWebSocketProtocolAndDomain();

export const trainDevShellUrl = `/web/develop/v1/getShellVisitUrl`;
export const trainTaskLogUrl = `/web/task/v1/pod/ws`;
export const trainTaskShellUrl = `/web/task/v1/pod/ws`;
export const serviceManageShellUrl = `/web/serving/v1/instance/ws/url`;
export const serviceManageLogUrl = `/web/serving/v1/instance/ws/url`;
