<template>
  <!-- eslint-disable vue/no-v-html -->
  <div v-if="logMessage" class="socket-log">{{ logMessage }}</div>
  <jt-empty v-else title="日志" :show-operation="true" />
</template>

<script setup>
import { defineProps, onMounted, onBeforeUnmount } from 'vue';
import { message } from 'ant-design-vue';
const INVALID_REASON = 'url invalid';
import { requestWithProjectId } from '@/request';
const { POST: requestWithProjectIdPOST } = requestWithProjectId;
import { keycloak } from '@/keycloak';

const props = defineProps({
  // 优先考虑url
  url: {
    // 直接可以访问的socket url
    type: String,
    default: '',
  },
  requestUrl: {
    // 从请求中获取url的方式
    type: String,
    default: '',
  },
  requestData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(['change']);

let socket = null;
let logMessage = ref('');
let createUrl = null;
let isDestroy = false; // 防止组件退出时有close提示
const logArray = [];
let renderIntervalId = null;
let firstReceive = true;

onMounted(async () => {
  if (!props.url) {
    const res = await requestWithProjectIdPOST(props.requestUrl, props.requestData);
    if (res.code === 0) {
      createUrl = res.data;
    }
  }
  createWebSocket();
  initRenderInterval();
});

onBeforeUnmount(() => {
  isDestroy = true;
  socket && socket.close();
  socket = null;
  renderIntervalId && clearInterval(renderIntervalId);
});

const createWebSocket = () => {
  socket = new WebSocket(props.url || createUrl, [encodeURIComponent(`Bearer ${keycloak.token}`)]);
  socket.onmessage = onMessage;
  socket.onclose = onClose;
};

const onMessage = (e) => {
  logArray.push(e.data);
  if(firstReceive) {
    firstReceive = false;
    renderLog();
  }
};

// 间隔1.5s进行log日志的渲染，防止后端频繁推送带来的卡顿性能问题
const initRenderInterval = () => {
  renderIntervalId = setInterval(renderLog, 1500);
};

const renderLog = () => {
  const len = logArray.length;
  if (len > 0) {
    const logStr = logArray.splice(0, len).join('');
    logMessage.value = logMessage.value + logStr.replace(/\\r\\n/g, '\n');
    emit('change', logMessage.value);
  }
};

const onClose = (e) => {
  if (isDestroy) {
    return;
  }
  if (e.code === 1007 && e.reason === INVALID_REASON) {
    message.error('链接已失效，请刷新重试');
  } else {
    message.error('连接异常，请刷新重试');
  }
};
</script>

<style lang="less" scoped>
.socket-log {
  height: calc(100% - 40px);
  white-space: pre-line;
  overflow: auto;
}
</style>
