<template>
  <div class="image-value-box">
    <div class="tag-box">
      <span class="type">运行实例</span><span class="num">{{ dataShow.replicas }}</span
      ><span class="unit">个</span>
    </div>
    <div><jt-icon type="iconguanbi" class="anticon"></jt-icon></div>
    <div class="tag-box">
      <div v-if="Number(dataShow.gpu) > 0" style="margin-right: 32px">
        <span class="type">AI加速卡</span><span class="num">{{ dataShow.gpu || defaultText }}</span
        ><span class="unit">{{ dataShow.gpuParam || defaultText }}</span>
      </div>
      <div style="margin-right: 32px">
        <span class="type">CPU</span><span class="num">{{ dataShow.cpu || defaultText }}</span
        ><span class="unit">核</span>
      </div>
      <div>
        <span class="type">内存</span><span class="num">{{ Number(dataShow.memory) / 1024 || defaultText }}</span
        ><span class="unit">GB</span>
      </div>
    </div>
  </div>
</template>
<script setup>
const defaultText = '--';
const props = defineProps({
  data: {
    type: Object,
    default() {
      return {};
    },
  },
});
const dataShow = ref({});
watch(
  () => props.data,
  (val) => {
    dataShow.value = val;
  },
  {
    deep: true,
  }
);
</script>
<style lang="less" scoped>
.image-value-box {
  display: flex;
  align-items: center;
  .tag-box {
    background: rgba(0, 20, 26, 0.02);
    border-radius: 2px;
    padding: 16px 20px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    .type {
      color: #606972;
      margin-right: 8px;
    }
    .num {
      font-size: 22px;
      font-weight: 600;
      color: #00141a;
    }
    .unit {
      color: #121f2c;
      margin-left: 4px;
    }
  }
  .anticon {
    color: #cbcfd2;
    margin-left: 18px;
    margin-right: 18px;
    font-size: 16px;
    transform: scale(1.5);
  }
}
</style>
