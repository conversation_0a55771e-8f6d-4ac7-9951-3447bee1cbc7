/**
 * @description: 节点状态
 * @return {*}
 */
export const NODE_STATUS = {
  1: '可用',
  0: '不可用',
  2: '禁止调度',
};

/**
 * @description: 节点状态tag颜色
 * @return {*}
 */
export const NODE_STATUS_COLOR = {
  0: 'red',
  1: 'green',
  2: 'red'
};

/**
 * @description: 新建操作状态
 * @return {*}
 */
export const NODE_STATUS_SCHEDULING = {
  PROHIBIT_SCHEDULING: '1', // 禁止调度
  RELEASE_SCHEDULING: '2', // 放开调度
};

export const NODE_STATUS_FILTER = {
  2: '放开调度',
  1: '禁止调度',
};