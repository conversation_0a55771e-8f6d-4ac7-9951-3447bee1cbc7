<template>
  <div class="jt-textarea">
    <a-textarea v-bind="$attrs" :maxlength="0" :show-count="false" />
    <div class="count-area">
      <span class="count">{{ ($attrs.value || '').length }}</span>
      <span class="max-length">{{ `/${$attrs.maxlength}` }}</span>
    </div>
  </div>
</template>

<script setup></script>

<style lang="less" scoped>
.jt-textarea {
  position: relative;
}

.count-area {
  position: absolute;
  right: 0;
  bottom: -25px;
  color: rgba(0, 20, 26, 0.45);
}
</style>

<style lang="less">
.ant-form-item-has-error.ant-form-item-with-help .count-area .count {
  color: #ff4d4f;
}
</style>
