<template>
  <svg :width="width" :height="height" :viewbox="viewbox">
    <polygon :points="points" class="rect" :class="rectClass"/>
  </svg>
</template>

<script setup>
const props = defineProps({
  size: {
    type: String,
    default: 'normal', // large-normal-small：大-中-小 尺寸
  },
});
const width = ref('40');
const height = ref('40');
const viewbox = ref('0 0 40 40');
const points = ref('0 0 0 40 40 40 40 0');
const rectClass = ref('');

watch(
  () => props.size,
  () => {
    if (props.size === 'large') {
      width.value = '48';
      height.value = '48';
      viewbox.value = '0 0 48 48';
      points.value = '0 0 0 48 48 48 48 0';
      rectClass.value = 'rectLarge';
    } else if (props.size === 'small') {
      width.value = '32';
      height.value = '32';
      viewbox.value = '0 0 32 32';
      points.value = '0 0 0 32 32 32 32 0';
      rectClass.value = 'rectSmall';
    }
  },
  {
    immediate: true,
  }
)

</script>
<style lang="less" scoped>
svg {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
}

.rect {
  fill: none;
  stroke-width: 8px;
  stroke: #00a0cc;
  stroke-dasharray: 40;
  stroke-dashoffset: 50%;
  animation: movedash 1.5s forwards 0s infinite;
}
.rect.rectLarge {
  stroke-dasharray: 48;
}
.rect.rectSmall {
  stroke-dasharray: 32;
}

@keyframes movedash {
  to {
    stroke-dashoffset: 250%;
  }
}
</style>
