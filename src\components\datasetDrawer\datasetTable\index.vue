<template>
  <a-tabs v-model:activeKey="activeKey">
    <a-tab-pane key="1">
      <template #tab>
        自定义数据集
        <span class="tab-title-num">{{ props.customTableData?.length || 0 }}</span>
      </template>
      <custom-dataset v-model:selectedRowKeys="customSelectedRowKeys" :table-data="props.customTableData" />
    </a-tab-pane>
    <a-tab-pane key="2">
      <template #tab>
        预置数据集
        <span class="tab-title-num">{{ props.presetTableData?.length || 0 }}</span>
      </template>
      <preset-dataset v-model:selectedRowKeys="presetSelectedRowKeys" :table-data="props.presetTableData" />
    </a-tab-pane>
    <a-tab-pane v-if="showDatasetShare" key="3">
      <template #tab>
        被分享数据集
        <span class="tab-title-num">{{ props.shareTableData?.length || 0 }}</span>
      </template>
      <share-dataset v-model:selectedRowKeys="shareSelectedRowKeys" :table-data="props.shareTableData" />
    </a-tab-pane>
  </a-tabs>
</template>
<script setup>
import { showDatasetShare } from '@/components/datasetDrawer/constant.js';

import customDataset from './customDataset.vue';
import presetDataset from './presetDataset.vue';
import shareDataset from './shareDataset.vue';

const props = defineProps({
  customTableData: {
    type: Array,
    default: () => [],
  },
  presetTableData: {
    type: Array,
    default: () => [],
  },
  shareTableData: {
    type: Array,
    default: () => [],
  },
});

const customSelectedRowKeys = defineModel('customSelectedRowKeys', { type: Array, default: () => [] });
const presetSelectedRowKeys = defineModel('presetSelectedRowKeys', { type: Array, default: () => [] });
const shareSelectedRowKeys = defineModel('shareSelectedRowKeys', { type: Array, default: () => [] });
const activeKey = ref('1');
</script>

<style lang="less" scoped>
.tab-title-num {
  margin-left: 8px;
}
// :deep(.ant-table-wrapper) {
//   min-height: calc(100vh - 56px - 24px - 16px - 40px - 48px - 80px);
// }
</style>
