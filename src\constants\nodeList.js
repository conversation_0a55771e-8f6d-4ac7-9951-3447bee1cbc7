// 资源组大类（公共资源组、专属资源组）
export const RESOURCE_GROUP_TYPE = {
  PUBLIC: 'public', // 公共资源组
  PERSONAL: 'personal', // 专属资源组
};
// 所属资源组（公共资源组、专属资源组）
export const RESOURCE_ATTRIBUTES = {
  public: '公共',
  private: '专属',
  no: '无',
};
//资源类型
export const SOURCE_TYPE = {
  gpucard: '加速卡',
  cpu: 'CPU',
};



export const SCENARIO_TYPE_MAP = {
  ALL: '0',
  TRAIN: '1',
  INFERENCE: '2',
  OPTIMIZE: '3',
  DATA: '4',
};

export const SCENARIO_TYPE_SORT = [SCENARIO_TYPE_MAP.DATA, SCENARIO_TYPE_MAP.TRAIN, SCENARIO_TYPE_MAP.OPTIMIZE, SCENARIO_TYPE_MAP.INFERENCE];

export const SCENARIO_TYPE = {
  [SCENARIO_TYPE_MAP.ALL]: '全有',
  [SCENARIO_TYPE_MAP.TRAIN]: '训练',
  [SCENARIO_TYPE_MAP.INFERENCE]: '推理',
  [SCENARIO_TYPE_MAP.OPTIMIZE]: '调优',
  [SCENARIO_TYPE_MAP.DATA]: '数据',
};

export const SCENARIO_TYPE_TAG_COLOR = {
  [SCENARIO_TYPE_MAP.TRAIN]: 'blue',
  [SCENARIO_TYPE_MAP.INFERENCE]: 'green',
  [SCENARIO_TYPE_MAP.OPTIMIZE]: 'purple',
  [SCENARIO_TYPE_MAP.DATA]: 'pink',
}
