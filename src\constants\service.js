// 新建服务-代码配置-读写权限
export const readOnlyAuthOptions = [
  {
    value: true,
    label: '是',
  },
  {
    value: false,
    label: '否',
  },
];

// 从汇聚过来的服务页面来源，汇聚模型与汇聚应用
export const QDLAKE_TYPE_ENUM = {
  QDLAKE_MODEL: 'QDLAKE_MODEL', // 汇聚模型
  QDLAKE_APP: 'QDLAKE_APP', // 汇聚应用
};
export const CHIP_TYPE_TEXT = {
  0: 'CPU', // CPU
  1: 'GPU', // GPU
  2: 'NPU', // NPU
  3: 'TPU', // TPU
  99: '--', // 之前的空数据情况
};

export const CHIP_TYPE_ENUM = {
  CPU: 0, // CPU
  GPU: 1, // GPU
  NPU: 2, // NPU
  TPU: 3, // TPU
  NONE: 99, // 空数据
};

export const ARCHITECTURE_TYPE_TEXT = {
  0: 'X86', // X86
  1: 'ARM', // ARM
};
// 跳转到新建服务的需要进行区分的页面来源
export const SOURCE_TYPE_ENUM = {
  KL_SERIVE_CREATE: undefined, // 新建服务 - 大模型平台
  KL_MODEL_DEPLOY: '1', // 模型部署 - 大模型平台
  HJ_APP_PUBLISH: '2', // 汇聚应用
  HJ_MODEL_DEPLOY: '3', // 汇聚模型
};

export const CARD_TYPE_ENUM = {
  CPU: 0,
  SHENGTENG: 1,
  NVIDIA: 2,
};
// 主机时区-容器内映射目录默认值
export const defaultTimeZoneContainerPathText = '/etc/localtime';
export const hostAddress = '/usr/share/zoneinfo/Asia/Shanghai';

// 汇聚模型和汇聚应用跳转过来需要的模型名称
export const defaultModelVersionFromHJ = 'V1';
// 服务部署的时候，volumes的type参数，模型选择：largeModel ，模型挂载：largeModelMount，汇聚模型下载：largeModelDownload
export const VOLUMES_TYPE_ENUM = {
  MOUNT: 'largeModelMount',
  SELECT: 'largeModel',
  QDLAKE: 'largeModelDownload',
  STORAGE: 'codeConfig',
};

// 表单的宽度
export const formWidth = '100%';
export const formEditTableWidth = '100%';
export const formLabelCol = { span: 3 };
export const formWrapperCol = { span: 20 };
export const resourceFormWrapperCol = { span: 17 };
export const inputNumLength = '220px';
export const commonHealthCheckLabelSpan = 6;
export const formLabelColForGroups = { span: 3 }; // 服务群组表单
export const formWrapperColForGroups = { span: 17 };

export const helps = {
  port: '请输入1-65535的数字',
  initialDelaySeconds: '默认为30',
  periodSeconds: '默认为10',
  timeoutSeconds: '默认为10',
  successThreshold: '默认为1',
  failureThreshold: '默认为3，最小为1',
  path: '请输入请求路径，例如/health',
  order: '命令行用逗号隔开，如cat,/tem/health',
  host: '请输入, 例如example.com',
};

export const rules = {
  port: [{ required: true, message: '请输入容器端口', trigger: ['blur', 'change'] }],
  path: [{ required: true, message: '请输入请求路径', trigger: ['blur', 'change'] }],
  order: [{ required: true, message: '请输入命令', trigger: ['blur', 'change'] }],
};
// 新建服务-模型配置等读写权限
export const MODEL_CONFIG_READONLY_OPTION = [
  {
    value: true,
    label: '读取',
  },
  {
    value: false,
    label: '读写',
  },
];

// 新建服务
// 模型部署-模型配置
export const MODEL_CONFIG_ENUM = {
  MOUNT: 'mount', // 模型挂载
  SELECT: 'option', // 模型选择
  QDLAKE: 'qdlake', // 汇聚模型下载
};

// 新建服务-镜像文件枚举
export const IMAGE_SELECT_ENUM = {
  PLATFORM_PRESET_IMAGE: 'preset', // 平台预置镜像
  CUSTOM_IMAGE: 'custom', // 自定义镜像
};
// 新建服务，平台预置框架和自定义框架
export const FRAMEWORK_SELECT_ENUM_TEXT_MAP = new Map([
  [IMAGE_SELECT_ENUM.PLATFORM_PRESET_IMAGE, '平台预置框架'],
  [IMAGE_SELECT_ENUM.CUSTOM_IMAGE, '用户自定义框架'],
]);

export const IMAGE_SELECT_ENUM_TEXT_MAP = new Map([
  [IMAGE_SELECT_ENUM.PLATFORM_PRESET_IMAGE, '平台预置镜像'],
  [IMAGE_SELECT_ENUM.CUSTOM_IMAGE, '自定义镜像'],
]);

// 新建服务-部署方式枚举
export const SERVICE_DEPLOY_TYPE_ENUM = {
  IMAGE: 'IMAGE', // 镜像部署
  MODEL: 'MODEL', // 模型部署
};
export const SERVICE_DEPLOY_TYPE_OPTION = [
  { value: SERVICE_DEPLOY_TYPE_ENUM.IMAGE, label: '镜像部署服务' },
  { value: SERVICE_DEPLOY_TYPE_ENUM.MODEL, label: '模型部署服务' },
];
// 在线调试-请求方式-下拉选项
export const REQUEST_TYPE_ENUM = {
  POST: 'POST',
  GET: 'GET',
};
export const REQUEST_TYPE_OPTION = [
  { value: REQUEST_TYPE_ENUM.GET, label: 'GET' },
  { value: REQUEST_TYPE_ENUM.POST, label: 'POST' },
];
// 在线调试-返回方式-下拉选项
export const STREAM_TYPE_ENUM = {
  STREAM: 'stream',
  NOT_STREAM: 'batch',
};
// 流式与非流式下拉选项
export const DEFAULT_STREAM_TYPE_OPTION = [
  { value: STREAM_TYPE_ENUM.NOT_STREAM, label: '非流式返回' },
  { value: STREAM_TYPE_ENUM.STREAM, label: '流式返回' },
];
// 服务日志-时间范围的内容
export const timeRanges = [
  {
    label: '自定义',
    value: 'custom',
  },
  {
    label: '近一小时',
    value: 'oneHour',
  },
  {
    label: '近一天',
    value: 'aDay',
  },
  {
    label: '近一周',
    value: 'sevenDays',
  },
];
// 模型推理-调用统计时间范围的内容
export const statisticsTimeOptions = [
  // {
  //   value: '自定义',
  //   label: '自定义',
  // },
  {
    value: 0,
    key: 0,
    label: '近一小时',
  },
  {
    value: 1,
    key: 1,
    label: '近一天',
  },
  {
    value: 2,
    key: 2,
    label: '近一周',
  },
  {
    value: 3,
    key: 3,
    label: '近一月',
  },
  {
    value: 4,
    key: 4,
    label: '近三个月',
  },
];

// 在线调试的Blob的type
export const REQUEST_CONTENT_TYPE_ENMU = {
  RAW: 'text/plain',
  PNG: 'image/png',
  JPG: 'image/jpg',
  JPEG: 'image/jpeg',
  GIF: 'image/gif',
  BMP: 'image/bmp',
  SVG: 'image/svg+xml',
  WEBP: 'image/webp',
  TIFF: 'image/tiff',
};
export const REQUEST_CONTENT_TYPE_MAP = new Map([
  [REQUEST_CONTENT_TYPE_ENMU.PNG, REQUEST_CONTENT_TYPE_ENMU.PNG],
  [REQUEST_CONTENT_TYPE_ENMU.JPG, REQUEST_CONTENT_TYPE_ENMU.JPG],
]);

// 在线调试的reponse的contentType
export const RESPONSE_CONTENT_TYPE_ENMU = {
  JSON: 'application/json',
};

// VOLUMES存储数据类型的区分
export const VOLUMES_STORAGE_TYPE_ENUM = {
  GENERAL_STOR: 'GENERAL_STOR', // 普通性能存储
  HS_STOR: 'HS_STOR', // 高性能存储
  EXTERNAL_STOR: 'EXTERNAL_STOR', // 外部存储
  OBJECT_STOR: 'OBJECT_STOR', // 对象存储
};

// 新建服务-部署状态
export const DEPLOY_STATUS_ENUM = {
  DEPLOYMENT: 'Deployment', // Deployment部署无状态应用
  STATEFULSET: 'StatefulSet', // StatefulSet部署有状态应用
};

export const healthCheckEnumMap = new Map([
  [1, 'TCP端口检查'],
  [2, 'HTTP请求状态检查(2xx或者3xx)'],
  [3, '容器中进程退出状态码检查(0)'],
]);

export const HEALTH_CHECK_ENUM = {
  TCP: 1, // TCP端口检查
  HTTP: 2, // HTTP请求状态检查(2xx或者3xx)
  POD: 3, // 容器中进程退出状态码检查(0)
};

// 详情页的pod状态
export const DETAIL_POD_STATUS_ENUM = {
  RUNNING: 'Running',
  PENDING: 'Pending',
  CRASHLOOPBACKOFF: 'CrashLoopBackOff',
  TERMINATING: 'Terminating',
  UNKNOWN: 'unknown',
};

// 详情页的pod状态文案对应
export const DETAIL_POD_STATUS_ENUM_TEXT_MAP = new Map([
  [DETAIL_POD_STATUS_ENUM.RUNNING, '运行中'],
  [DETAIL_POD_STATUS_ENUM.PENDING, '启动中'],
  [DETAIL_POD_STATUS_ENUM.TERMINATING, '停止中'],
  [DETAIL_POD_STATUS_ENUM.CRASHLOOPBACKOFF, '失败'],
  [DETAIL_POD_STATUS_ENUM.UNKNOWN, 'unknown'],
]);

// 模型选择-平台预置模型和自定义模型
export const MODEL_SELECT_TYPE_ENUM = {
  PRESET_MODEL: 'preset_model', // 平台预置模型
  CUSTOM_MODEL: 'custom_model', // 自定义模型
};

// 自定义模型分类，这里增量预训练与有监督微调英文枚举反了，但是为了部署的时候，就没有更改
export const CUSTOM_MODEL_SOURCE_TYPE_ENUM = {
  FILE_GENERAL_STORE: '0', //普通文件存储
  FILE_HS_STORE: '1', // 高性能文件存储
  OBJECT_STORE: '2', // 对象存储
  FILE_EXTERNAL_STORE: '3', // 外部存储
  MODEL_COMPRESS: '4', // 模型压缩
  SUPERVISED_FINE_TUNING: '5', // 增量预训练
  PRETRAINING: '6', // 有监督微调
  PREFERENCE_ALIGNMENT: '7', // 偏好对齐
  KNOWLEDGE_DISTILLATION: '8', // 模型蒸馏
};

// 自定义模型下面的压缩模型，有监督微调，增量预训练，偏好对齐的枚举
export const CUSTOM_MODEL_SOURCE_TYPE_TEXT_MAP = new Map([
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.FILE_GENERAL_STORE, '文件存储'],
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.FILE_HS_STORE, '文件存储'],
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.OBJECT_STORE, '对象存储'],
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.FILE_EXTERNAL_STORE, '文件存储'], // 这里最真实的应该是外部存储，但是实际上文案映射成了文件存储
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.MODEL_COMPRESS, '模型压缩'],
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.SUPERVISED_FINE_TUNING, '增量预训练'],
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.PRETRAINING, '有监督微调'],
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.PREFERENCE_ALIGNMENT, '偏好对齐'],
]);

// 预置模型与自定义模型下面的压缩模型，有监督微调，增量预训练的枚举，部署服务和编辑服务的时候需要用
export const CUSTOM_MODEL_SOURCE_TYPE_DEPLOY_MAP = new Map([
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.MODEL_COMPRESS, 'MODEL_COMPRESS'],
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.SUPERVISED_FINE_TUNING, 'SUPERVISED_FINE_TUNING'],
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.PRETRAINING, 'PRETRAINING'],
]);

// 预置模型与自定义模型下面的压缩模型，有监督微调，增量预训练，偏好对齐在获取镜像关系与资源的时候需要的枚举值
export const CUSTOM_MODEL_SOURCE_TYPE_IMAGE_RESOURCE_ENUM = {
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.MODEL_COMPRESS]: 'MODEL_COMPRESS', // 模型压缩
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.SUPERVISED_FINE_TUNING]: 'PRETRAINING', // 增量预训练
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.PRETRAINING]: 'SUPERVISED_FINE_TUNING', // 有监督微调
  [CUSTOM_MODEL_SOURCE_TYPE_ENUM.PREFERENCE_ALIGNMENT]: 'PREFERENCE', // 偏好对齐
};
// 服务列表-服务类型筛选值
export const SERVICE_TYPE_ENUM = {
  MIRROR_SERVICE: 'IMAGE', // 镜像服务
  MODEL_SERVICE: 'MODEL', // 模型服务
};
export const SERVICE_TYPE_FILTER = [
  { text: '镜像服务', value: SERVICE_TYPE_ENUM.MIRROR_SERVICE },
  { text: '模型服务', value: SERVICE_TYPE_ENUM.MODEL_SERVICE },
];

export const SERVICE_TYPE_ENUM_TEXT = new Map([
  [SERVICE_TYPE_ENUM.MIRROR_SERVICE, '镜像服务'],
  [SERVICE_TYPE_ENUM.MODEL_SERVICE, '模型服务'],
]);

// 服务指标，按服务还是按实例筛选
export const MONITOR_SEARCH_TYPE_ENUM = {
  SERVICE: 'SERVICE', // 按服务
  POD: 'POD', // 按实例
};
// 服务指标，按服务还是按实例筛选
export const MONITOR_SEARCH_TYPE_OPTIONS = [
  {
    value: MONITOR_SEARCH_TYPE_ENUM.SERVICE,
    label: '按服务',
  },
  {
    value: MONITOR_SEARCH_TYPE_ENUM.POD,
    label: '按实例',
  },
];

export const defaultText = '-';

// 服务群组-流量分配策略
export const ALLOCATION_POLICY_ENUM = {
  AUTO: 'auto', // 自动
  HAND_MOVEMENT: 'manual', // 手动
};

// 服务群组-选择服务的弹层样式
export const drawerBodyStyle = {
  paddingTop: '0',
  paddingBottom: '0',
};
// 服务群组-选择服务的弹层样式
export const drawerTableScroll = { y: 'calc(100vh - 56px - 60px - 9px - 47px - 80px)' };
// export const drawerTableScroll = { y: 'calc(100vh - 56px - 60px - 9px - 47px - 80px - 48px)' };

// 服务限流类型枚举值
export const REQUEST_CURRENT_LIMIT_TYPE = {
  UNLIMITED_FLOW: 'UNLIMITED_FLOW', // 不限流
  SINGLE_INSTANCE_QPS: 'SINGLE_INSTANCE_QPS', // 单副本限流
};

// 服务群组-服务安全开关
export const SERVICE_SAFE_CHECK_ENUM = {
  OPEN: true, // 开启
  CLOSE: false, // 关闭
};
export const SERVICE_SAFE_CHECK_FILTER = [
  { text: '开启', value: SERVICE_SAFE_CHECK_ENUM.OPEN },
  { text: '关闭', value: SERVICE_SAFE_CHECK_ENUM.CLOSE },
];
