import { requestWithPoolId } from '@/request';
const { GET: requestWithPoolIdGet, POST: requestWithPoolIdPost } = requestWithPoolId;

// 新建资源组时全量查询某个资源池下可关联的项目列表
export const getListProjectApi = (data) => requestWithPoolIdPost('/web/admin/project/v1/pool/list-all-project', data);
// 新建资源组-获取加速卡类型接口示例0604
export const getGpuCardTypeApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/gpu-card-type', data);

// 获取集群信息
export const getClusterApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/cluster-info', data);
// 校验资源组名称是否存在
export const getGroupNameExistApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/is-group-name-exist', data);

// 新建资源组-获取节点信息列表
/* 
{
  "clusterId": 1,//集群id
  "nodeType": "1",//节点类型: (CPU类型资源组:传递"cpu"字符串, 加速卡类型资源组: 传递对应的加速卡类型id）
  "keyWord": "",//搜索关键字
  "status": []//节点状态: 1可用，0不可用
}
*/
export const getNodeListApi = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-not-bound-node-list', data);

// 新建资源组
export const createResourceGroupApi = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/create-resource-group', data);

export const getResourceGroupCreateStateApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/get-resource-group-state', data);

// 管理资源组-编辑专属资源组可关联项目
export const updatePrivateResourceGroupProject = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/update-private-resource-group-project', data);

// 分页获取资源组节点列表
export const getResourceGroupNodeList = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-resource-group-node-list', data);

// 获取资源组详细信息
export const resourceGroupDetail = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/resource-group-detail', data);

// 编辑资源组-获取节点信息列表
export const getEditResourceGroupNodeList = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/get-edit-resource-group-node-list', data);

// 管理资源组-编辑资源组节点
export const updateResourceGroupNode = (data) => requestWithPoolIdPost('/web/admin/resource/v1/manage/update-resource-group-node', data);

// V2.1.0版本增加资源池管理限制，当前资源池未分配相关资源时相关资源类型可用
// 获取资源池下创建资源组的资源类型信息
export const getPoolReourceGroupTypeApi = (data) => requestWithPoolIdGet('/web/admin/resource/v1/manage/get-pool-create-group-type', data);

export const EXPORT = {
  NODE: '/web/admin/resource/v1/node/list/export', //节点信息
  GROUP: '/web/admin/resource/v1/group/list/export', //资源组信息
};
