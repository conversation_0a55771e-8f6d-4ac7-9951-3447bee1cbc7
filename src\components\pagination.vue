<template>
  <div v-if="total" class="pagination">
    <a-space :size="gapSize">
      <span>共 {{ total }} 条</span>
      <span v-if="showSizeOptions">
        每页显示
        <a-select
          :value="pageSize"
          style="width: 60px"
          :options="pageSizeOptions.map((item) => ({ label: item, value: item }))"
          :get-popup-container="
            (triggerNode) => {
              return triggerNode.parentNode;
            }
          "
          @change="changePageSize"
        ></a-select>
        条
      </span>
    </a-space>
    <a-pagination :page-size="pageSize" show-quick-jumper :current="pageNum" :total="total" :show-size-changer="false" @change="changePageNum" />
  </div>
</template>
<script>
import { defineComponent } from 'vue';
export default defineComponent({
  props: {
    total: {
      type: Number,
      default: 0,
    },
    pageNum: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    pageSizeOptions: {
      type: Array,
      default: () => [5, 10, 20],
    },
    showQuickJumper: {
      type: [Boolean, Object],
      default: false,
    },
    showSizeOptions: {
      type: Boolean,
      default: true,
    },
    gapSize: {
      type: String,
      default: 'large',
    },
  },
  emits: ['change', 'update:pageNum', 'update:pageSize', 'changePageSize', 'changePageNum'],
  setup(props, context) {
    const changePageSize = (size) => {
      context.emit('update:pageSize', size);
      context.emit('changePageSize', size);
    };
    const changePageNum = (pageNum) => {
      context.emit('update:pageNum', pageNum);
      context.emit('changePageNum', pageNum);
    };
    return {
      changePageSize,
      changePageNum,
    };
  },
});
</script>
<style lang="less" scoped>
.pagination {
  display: flex;
  justify-content: space-between;
  padding: 16px 0px;
  padding-bottom: 0;
  width: 100%;
}
</style>
