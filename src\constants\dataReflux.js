// 任务类型，就我创建还是全部创建
export const MISSION_TYPE_ENUM = {
  ONLY_ME: '1', // 仅我创建
  ALL_CREATE: '2', // 全部
};

// 回流任务列表页面中选择的方式
export const MISSION_TYPE_OPTION = [
  { value: MISSION_TYPE_ENUM.ONLY_ME, label: '仅我创建' },
  { value: MISSION_TYPE_ENUM.ALL_CREATE, label: '全部' },
];

// 列表页的数据格式-问答对、图文对
export const DATA_FORMAT_ENUM = {
  Q_AND_A: 0, // 问答对
  IMAGE_AND_TEXT: 1, // 图文对
};

// 列表页的数据格式筛选
export const DATA_FORMAT_FILTER = [
  { text: '问答对', value: DATA_FORMAT_ENUM.Q_AND_A },
  { text: '图文对', value: DATA_FORMAT_ENUM.IMAGE_AND_TEXT },
];

// 详情页-基础信息-数据类型
export const DATA_FORMAT_TEXT_MAP = new Map([
  [DATA_FORMAT_ENUM.Q_AND_A, '问答对'],
  [DATA_FORMAT_ENUM.IMAGE_AND_TEXT, '图文对'],
]);

// 详情页-保存弹窗-保存类型
export const SAVE_TYPE_ENUM = {
  OLD_DATASET: '1', // 已有数据集
  NEW_DATASET: '2', // 新的数据集
};
// 详情页-保存弹窗-数据集模式
export const DATASET_SCHEMA = {
  YES_SCHEMA: 1, // 流模式
  NO_SCHEMA: 0, // 非流模式
};
