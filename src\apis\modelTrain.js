import { GET, requestWithProjectId } from '@/request';
const { POST, requestBlob, GET: requestWithProjectIdGet } = requestWithProjectId;

// 检查是否有正在保存的镜像
export const saveImageCheck = (data) => requestWithProjectIdGet('/web/develop/v1/saveImageCheck', data);
// 开发环境保存自定义镜像
export const saveCustomImage = (data) => POST('/web/develop/v1/save', data);
// 开发环境查询实例事件
export const getDevEvents = (data) => POST('/web/develop/v1/events', data);
// 获取开发环境最大限制卡数
export const getGpuCardMax = (data) => GET('/web/develop/v1/gpuCardMax', data);
// 获取可视化tensorboard路径
export const getTensorboard = (data) => POST('/web/develop/v1/tensorboard', data);
// 训练任务查询实例事件
export const getTaskEvents = (data) => POST('/web/task/v1/pod/events', data);
// 训练任务查询日志
export const getTaskPodLog = (data) => POST('/web/task/v1/pod/log', data);
// 训练任务导出日志
export const exportTaskPodLog = (data) => requestBlob('/web/task/v1/pod/download', data, { responseType: 'blob' });
// 训练任务修改优先级
export const updatePriority = (data) => requestWithProjectIdGet('/web/task/v1/update-priority', data);
