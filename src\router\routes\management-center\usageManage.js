import { MGT_USAGE_VIEW_AUTH } from '@/constants/usage';

// 运管中心-用量管理
const USAGE_MANAGE = [
  {
    path: '/usage-manage',
    name: 'usage-manage',
    component: () => import('@/views/management-center/usage-manage/index.vue'),
    meta: {
      header: [
        {
          name: '用量管理',
        },
      ],
    },
    children: [
      {
        path: '',
        name: 'usage',
        component: () => import('@/views/management-center/usage-manage/list/index.vue'),
        meta: {
          header: [
            {
              name: '运管中心',
            },
            {
              name: '用量管理',
            },
          ],
        },
      },
    ]
  },
  // 项目活动详情
  {
    path: '/usage-manage/detail/:id/:projectId',
    name: 'activity-detail',
    component: () => import('@/views/management-center/usage-manage/detail/index.vue'),
    meta: {
      header: [
        {
          name: '运管中心',
        },
        {
          name: '用量管理',
          path: '/usage-manage',
          query: {
            tab: 3,
          }
        },
        {
          name: `项目活动用量明细`,
        },
      ],
      apvAndMgtPermission: MGT_USAGE_VIEW_AUTH,
    },
  }
];

export default USAGE_MANAGE;
