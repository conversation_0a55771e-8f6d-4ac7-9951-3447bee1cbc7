"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;

var _jtIcon = _interopRequireDefault(require("./jtIcon"));

var _statusTag = _interopRequireDefault(require("./statusTag.vue"));

var _pagination = _interopRequireDefault(require("./pagination.vue"));

var _empty = _interopRequireDefault(require("./empty.vue"));

var _listBanner = _interopRequireDefault(require("./listBanner.vue"));

var _reloadIconBtn = _interopRequireDefault(require("./reloadIconBtn.vue"));

var _container = _interopRequireDefault(require("./container.vue"));

var _containerItem = _interopRequireDefault(require("./containerItem.vue"));

var _searchInput = _interopRequireDefault(require("./searchInput.vue"));

var _Loading = _interopRequireDefault(require("./Loading.vue"));

var _xTerminal = _interopRequireDefault(require("./xTerminal.vue"));

var _socketLog = _interopRequireDefault(require("./socketLog.vue"));

var _subHeader = _interopRequireDefault(require("./subHeader.vue"));

var _welcomePage = _interopRequireDefault(require("./welcomePage.vue"));

var _jtTag = _interopRequireDefault(require("./jtTag.vue"));

var _index = _interopRequireDefault(require("./micro-components/index.vue"));

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { "default": obj }; }

var _default = {
  // eslint-disable-next-line no-unused-vars
  install: function install(app, options) {
    app.component('JtIcon', _jtIcon["default"]);
    app.component('JtStatusTag', _statusTag["default"]);
    app.component('JtPagination', _pagination["default"]);
    app.component('JtEmpty', _empty["default"]);
    app.component('JtListBanner', _listBanner["default"]);
    app.component('JtReloadIconBtn', _reloadIconBtn["default"]);
    app.component('JtContainer', _container["default"]);
    app.component('JtContainerItem', _containerItem["default"]);
    app.component('JtSearchInput', _searchInput["default"]);
    app.component('JtLoading', _Loading["default"]);
    app.component('JtXterminal', _xTerminal["default"]);
    app.component('JtSocketLog', _socketLog["default"]);
    app.component('JtSubHeader', _subHeader["default"]);
    app.component('JtWelcomePage', _welcomePage["default"]);
    app.component('JtTag', _jtTag["default"]);
    app.component('JtMicroComponents', _index["default"]);
  }
};
exports["default"] = _default;