<template>
  <a-table :data-source="innerData" :bordered="true" :pagination="false" align="center" size="small" :row-key="rowKey" :default-expand-all-rows="true" :columns="wrapedColumns" class="table-wrapper" :expanded-row-keys="expandedRowKeys" :show-expand-column="false">
    <template #headerCell="{ column }">
      <slot :name="`${column.key}Header`"></slot>
    </template>

    <template v-if="needDescription" #expandedRowRender="{ record, index }">
      <div class="expended-row">
        <div v-if="!innerEditMode || (innerEditMode && !isLastIndex(index))" :class="{ 'no-bottom-border': isEditRow(index + 1), 'description-margin': isEditRow(index + 1) }" class="description">
          <slot name="expandedRowRender" :record="record"></slot>
        </div>
        <slot v-if="isEditRow(index)" name="expandedRowRenderFormItem"></slot>
      </div>
    </template>

    <template #bodyCell="{ column, record, index }">
      <template v-if="column.key === 'add' && isLastIndex(index)">
        <a-button class="operate-btn" :disabled="innerEditMode || noMore" @click="add">
          <jt-icon type="icontianjia" />
        </a-button>
      </template>
      <template v-if="column.key === 'remove'">
        <a-button class="operate-btn" :disabled="record.unremovable" @click="remove(index)">
          <jt-icon type="iconsubtract-line" />
        </a-button>
      </template>
      <template v-else-if="column.key === 'copyOrSave'">
        <a-button v-if="record.copyable" class="operate-btn" :disabled="!innerEditMode" @click="copy(record)">
          <jt-icon type="iconfuzhi" />
        </a-button>
        <a-button v-else-if="isEditRow(index)" class="operate-btn" @click="save">
          <jt-icon type="iconcheck" />
        </a-button>
        <a-button v-else-if="!record.example" class="operate-btn" :disabled="record.uneditable || innerEditMode" @click="edit(record)">
          <jt-icon type="iconbianji" />
        </a-button>
      </template>
      <template v-else>
        <template v-if="!record.example && isEditRow(index)">
          <slot :name="`${column.key}FormItem`"></slot>
        </template>
        <template v-if="!isEditRow(index)">
          <a-flex class="record-cell">
            <slot :name="`${column.key}Record`" :record="record"></slot>
          </a-flex>
        </template>
      </template>
    </template>
  </a-table>
</template>

<script setup>
import { message } from 'ant-design-vue';
import { useStore } from 'vuex';
import { stringUnix } from '@/utils/index';
const store = useStore();
const props = defineProps({
  // 表格的列表头
  columns: {
    type: Array,
    default() {
      return [];
    },
  },
  // 表格需要展示的数据
  data: {
    type: Array,
    default() {
      return [];
    },
  },
  rowKey: {
    type: String,
    default: 'id',
  },
  // 是否为编辑模式
  editMode: {
    type: Boolean,
    default: false,
  },
  emptyForm: {
    type: Object,
    default() {
      return {};
    },
  },
  needDescription: {
    type: Boolean,
    default: false,
  },
  // 最多添加多少数据
  max: {
    type: Number,
    default: 20,
  },
  // 添加按钮是否禁用
  addDisabled: {
    type: Boolean,
    default: false,
  },
  // 添加按钮的禁用信息
  addDisabledMessage: {
    type: String,
    default: '',
  },
  // 刷新参数
  needUpdateEmptyForm: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(['change', 'editModeChange', 'resetFields', 'copy', 'save']);

const { data, editMode, max, addDisabled, addDisabledMessage, columns, rowKey } = toRefs(props);

// 表格数据
const innerData = ref([...data.value]);
// 是否为编辑模型
const innerEditMode = ref(editMode.value);
const operationColumns = ['add', 'remove', 'copyOrSave'];

watch(
  () => props.data,
  (newValue) => {
    innerData.value = [...toRaw(newValue)];
  },
  {
    immediate: true,
    deep: true,
  }
);
watch(
  () => props.editMode,
  (newValue) => {
    innerEditMode.value = newValue;
  }
);

// 表头的列
const wrapedColumns = computed(() => {
  const columnsNew = columns?.value?.map((col) => {
    const colItem = Object.assign({}, col);
    colItem.customCell = customCell();
    colItem.customHeaderCell = customHeaderCell();
    return colItem;
  });

  const addColumn = {
    title: null,
    key: 'add',
    align: 'right',
    width: '32px',
    customHeaderCell: customHeaderCell(),
    customCell: customOperationCell('add'),
  };
  const removeColumn = {
    title: null,
    key: 'remove',
    align: 'right',
    width: '32px',
    customHeaderCell: customHeaderCell(),
    customCell: customOperationCell('remove'),
  };
  const copyOrSaveColumn = {
    title: null,
    key: 'copyOrSave',
    align: 'left',
    width: '32px',
    customHeaderCell: customHeaderCell(),
    customCell: customOperationCell('copyOrSave'),
  };
  columnsNew.unshift(removeColumn);
  columnsNew.unshift(addColumn);
  columnsNew.push(copyOrSaveColumn);
  console.log(columnsNew);
  return columnsNew;
});

const noMore = computed(() => {
  return innerData.value.length >= max.value;
});

const expandedRowKeys = computed(() => {
  return innerData.value?.map((item) => item[toRaw(rowKey.value)]);
});

const customCell = () => {
  return (record, rowIndex) => {
    const config = {};
    config.style = {};
    config.style = {
      padding: '0 12px',
      // border: 'none',
      height: '32px',
    };
    if (isEditRow(rowIndex)) {
      config.style.padding = '0';
    }
    // if (isFirstIndex(rowIndex)) {
    //   config.style.borderBottom = '1px solid #e8e8e8';
    // }
    // // 倒数第2行样式
    // if (isLastRecordRow(rowIndex) && editMode.value) {
    //   config.style.borderBottom = 'none';
    // }
    return config;
  };
};

const customHeaderCell = () => {
  return (column) => {
    const config = {};
    const key = column.key;
    config.style = {
      border: 'none',
      background: 'transparent',
      borderBottom: '1px solid #e8e8e8',
    };
    if (key === 'add' && innerData.value.length > 1) {
      config.style.borderBottom = 'none';
    }
    return config;
  };
};

const customOperationCell = (key) => {
  const checkRemove = () => {
    return key === 'remove';
  };
  const checkCopy = (record) => {
    return record.copyable;
  };
  const checkSave = (record, index) => {
    return isEditRow(index);
  };
  const checkCopyOrSave = (record, index) => {
    return key === 'copyOrSave' && (checkCopy(record) || checkSave(record, index));
  };
  const checkAdd = (index) => {
    return key === 'add' && isLastIndex(index);
  };
  const checkEdit = (record, index) => {
    return key === 'copyOrSave' && !record.unremovable && !isEditRow(index);
  };
  const checkOperation = (record, index) => {
    return checkAdd(index) || checkRemove() || checkCopyOrSave(record, index) || checkEdit(record, index) || false;
  };
  return (record, rowIndex) => {
    const config = {};
    config.style = {
      // border: 'none',
      padding: '0',
      background: 'transparent',
    };
    // return config;

    // if (checkOperation(record, rowIndex)) {
    //   config.style.border = '1px solid #e8e8e8';
    // }
    // if (key === 'remove') {
    //   config.style.borderRight = '1px solid #e8e8e8';
    //   if (isFirstIndex(rowIndex)) {
    //     config.style.borderLeft = '1px solid #e8e8e8';
    //     config.style.borderBottom = '1px solid #e8e8e8';
    //     config.style.borderTop = '1px solid #e8e8e8';
    //   }
    // }
    // if (key === 'copyOrSave') {
    //   config.style.borderLeft = 'none';
    //   if (!isFirstIndex(rowIndex) && !isLastIndex(rowIndex)) {
    //     config.style.borderTop = 'none';
    //   }
    // }
    if (key === 'add') {
      if (isLastIndex(rowIndex)) {
        config.style.borderLeft = '1px solid #e8e8e8';
      }
      if (rowIndex < innerData.value.length - 2) {
        config.style.borderBottom = 'none';
      }
    }
    return config;
  };
};

// 判断是否为最后一条数据
const isLastIndex = (index) => {
  return index === innerData.value.length - 1;
};

// 判断是否为最初一条数据
const isFirstIndex = (index) => {
  return index === 0;
};

const add = () => {
  if (addDisabled.value) {
    if (addDisabledMessage.value) {
      message.warning(addDisabledMessage.value);
    }
    return;
  }
  innerData.value = [...toRaw(innerData.value), getEmptyItem()];
  emits('editModeChange', true);
  emits('change', toRaw(innerData.value), getEmptyItem());
  // innerEditMode.value = true;
  // emits('editModeChange', innerEditMode.value);
  emits('resetFields');
};

const getEmptyItem = () => {
  if (props.needUpdateEmptyForm) {
    return { ...toRaw(props.emptyForm), name: stringUnix(store.state.projectId) };
  }
  return { ...toRaw({ ...props.emptyForm }) };
};

const edit = (record) => {
  toRaw(innerData.value).forEach((item, index) => {
    if (item[rowKey.value] == record[rowKey.value] && !item.example) {
      innerData.value.splice(index, 1);
    }
  });
  innerData.value = [...toRaw(innerData.value), toRaw(record)];
  emits('resetFields');
  emits('editModeChange', true);
  emits('change', toRaw(innerData.value), toRaw(record), 'edit');
  // innerEditMode.value = true;
  // emits('editModeChange', innerEditMode.value);
};
const remove = (index) => {
  if (isEditRow(index)) {
    // this.innerEditMode = false;
    // $emit(this, 'editModeChange', this.innerEditMode);
    emits('editModeChange', false);
    emits('resetFields');
  }
  innerData.value.splice(index, 1);
  emits('change', toRaw(innerData.value));
};
// 判断当前行
const isEditRow = (index) => {
  return isLastIndex(index) && innerEditMode.value;
};

const copy = (record) => {
  emits('resetFields');
  emits('copy', record);
};

// 倒数第2行数据
const isLastRecordRow = (index) => {
  return index === innerData.value.length - 2;
};
const save = () => {
  emits('save');
};
</script>

<style lang="less" scoped>
:deep(.ant-table.ant-table-bordered > .ant-table-container) {
  border-inline-start: none;
}
:deep(.ant-alert-with-description .ant-alert-message) {
  margin-bottom: 0;
}
:deep(.ant-table-bordered) {
  border: none;
}
:deep(.ant-table-bordered .ant-table-content) {
  border: none;
}
:deep(.ant-table-content > .ant-table-body > table > .ant-table-tbody > tr > td) {
  padding: 6.5px 8px;
}
:deep(.operate-btn) {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  width: 100%;
  height: 30px;
  border: 0;
  box-shadow: none;
}
:deep(.ant-form-item-control) {
  line-height: 32px;
}

:deep(.ant-form-explain) {
  position: absolute;
}

:deep(.ant-form-item-with-help) {
  margin-bottom: 0;
}

:deep(.ant-form-item) {
  position: absolute;
  left: -0.5px;
  top: -0.5px;
  width: calc(100% + 0.5px);
  margin-bottom: 0;
}
:deep(.ant-form-item .ant-input) {
  box-sizing: border-box;
  height: 32.5px;
  border-radius: 0;
  border-color: #f0f0f0;
}

:deep(.ant-form-item .ant-select-selector) {
  box-sizing: border-box;
  height: 32.5px;
  border-radius: 0;
  border-color: #f0f0f0;
}

:deep(.ant-form-item .ant-input:hover) {
  border-color: #23b7d9;
}

:deep(.ant-form-item .ant-input:focus) {
  border-color: #23b7d9;
}

:deep(.ant-table-expanded-row.ant-table-expanded-row-level-1 td) {
  padding: 0 !important;
  border-bottom: none !important;
  border-inline-end: none !important;
}

:deep(.ant-table .ant-table-row-indent + .ant-table-row-expand-icon) {
  display: none;
}

:deep(.expended-row) {
  // 注意，这里要往左移1px
  padding-left: 63px;
  padding-right: 32px;
  background: #fff;
  // background: #fbfbfb;
}

:deep(.description-margin) {
  // margin-top: 30px;
}
:deep(.description) {
  padding: 6.5px 8px;
  // background: #fbfbfb;
  background: #fff;
  border-left: 1px solid #e8e8e8;
  border-right: 1px solid #e8e8e8;
  border-bottom: 1px solid #e8e8e8;
  :deep(&.no-bottom-border) {
    border-bottom: none;
  }
  white-space: pre-line;
}

// :deep(.form-item) {
//   // border-radius: 0;
//   // border-left: none;
// }

:deep(.description-form-item) {
  margin-top: 24px;
}

.table-wrapper {
  // padding-bottom: 24px;
  :deep(.ant-input-number) {
    width: auto;
    line-height: 2;
  }
  :deep(.ant-form-item .ant-form-item-explain-error) {
    position: absolute;
  }
  :deep(table) {
    border-top: none !important;
  }
}

:deep(.ant-btn[disabled]) {
  // background: transparent;
}

.iconFontSize {
  font-size: 12px;
}
.record-cell {
  // position: absolute;
  align-items: center;
  height: 32px;
}
</style>
