<template>
  <div class="detail-sub-header-container">
    <div class="title" :style="style">{{ titleShow }}</div>
    <div v-if="show" class="text" :style="style">{{ titleText }}</div>
    <slot></slot>
  </div>
</template>
<script setup>
import { ref } from 'vue';

const props = defineProps({
  // 需要展示的数据
  title: {
    type: String,
    default() {
      return '';
    },
  },
  size: {
    type: Number,
    default() {
      return 14;
    },
  },
  show: {
    type: Boolean,
    default() {
      return false;
    },
  },
  text: {
    type: String,
    default() {
      return '';
    },
  },
});
const titleShow = ref(props.title);
const size = ref(props.size);
const show = ref(props.show);
const titleText = ref(props.text);
const style = computed(() => {
  return {
    fontSize: `${size.value}px`,
    lineHeight: `${size.value}px`,
  };
});
watch(
  () => props.title,
  (newVal) => {
    titleShow.value = newVal;
  }
);
watch(
  () => props.show,
  (newVal) => {
    show.value = newVal;
  }
);
watch(
  () => props.text,
  (newVal) => {
    titleText.value = newVal;
  }
);
</script>
<style lang="less" scoped>
.detail-sub-header-container {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  .title {
    padding-left: 8px;
    border-left: 4px solid @jt-primary-color;
    font-weight: 600;
    color: #00141a;
  }
  .text {
    padding-left: 32px;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 20, 26, 0.45);
  }
}
</style>
