<template>
  <span>
    {{ currentTime || EMPTY_PLACERHOLDER }}
  </span>
</template>

<!-- 根据传入日期字符串，按秒进行自增 -->
<script setup>
import { ref, onMounted, onUnmounted, defineProps, watch } from 'vue';
const EMPTY_PLACERHOLDER = '--';
const props = defineProps({
  startTime: {
    // 格式要求：小时：分钟：秒
    type: String,
    default: '',
  },
});

let timer = null;
const currentTime = ref(props.startTime);

watch(
  () => props.startTime,
  () => {
    if (props.startTime && props.startTime !== EMPTY_PLACERHOLDER) {
      timer && clearInterval(timer);
      currentTime.value = props.startTime;
      timer = setInterval(incrementTime, 1000);
    }
  }
);

const incrementTime = () => {
  const [hours, minutes, seconds] = currentTime.value.split(':').map(Number);

  let totalSeconds = hours * 3600 + minutes * 60 + seconds + 1;

  const newHours = Math.floor(totalSeconds / 3600);
  const newMinutes = Math.floor((totalSeconds % 3600) / 60);
  const newSeconds = totalSeconds % 60;

  currentTime.value = `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}:${String(newSeconds).padStart(2, '0')}`;
};

onMounted(() => {
  if (currentTime.value && currentTime.value !== EMPTY_PLACERHOLDER) {
    timer = setInterval(incrementTime, 1000);
  }
});

onUnmounted(() => {
  timer && clearInterval(timer);
});
</script>

<style lang="less" scoped></style>
