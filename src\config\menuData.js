import { ROLE_KEYS } from '@/constants/projectSpace';
// 配置说明
// 1. 菜单的link，对应路由的path
// 2. 菜单的subs，表示子菜单，如果没有子菜单，则表示按钮
// 3. 菜单的space，表示菜单的类型，'pool'表示资源池，即项目空间外，'project'表示项目空间内，'platform'表示独立于资源池以外的运管平台
// 4. 菜单的role，表示菜单需要的的项目角色权限，只针对项目空间内有效，目前角色有：pm、po、develop,label，后续可能会有更多
// 5. 菜单tabAuth，表示菜单里面的每个 tab 权限 key，当所有权限都不存在于 store 中的 poolPolicy 时，菜单影藏

// 概览
const HOME = [
  {
    icon: 'icongailan1',
    title: '概览',
    link: '/home',
    linkFlag: 0,
    subs: [],
    space: 'pool',
    role: [],
  },
];

// 项目空间
const PROJECT_SPACE = [
  {
    icon: 'iconbox-fill',
    title: '项目空间管理',
    link: '/project-space',
    linkFlag: 0,
    subs: [],
    space: 'pool',
    role: [],
  },
  {
    icon: 'iconcontainer-fill',
    title: '项目空间详情',
    link: '/project-space-details',
    linkFlag: 0,
    subs: [],
    space: 'project',
    role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
  },
];

// 模型广场
const MODEL_SQUARE = [
  {
    icon: 'iconmoxingguangchang-daohang',
    title: '模型广场',
    link: '/model-square',
    linkFlag: 0,
    subs: [],
    space: 'project',
    role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
  },
];

// 模型体验
const MODEL_EXPERIENCE = [
  {
    icon: 'iconmessage-fill',
    title: '模型体验',
    link: '/model-experience',
    linkFlag: 0,
    subs: [],
    space: 'project',
    role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
  },
];

// 数据准备
const DATA_PREPARATION = [
  {
    icon: 'iconshujuzhunbei',
    title: '数据准备',
    link: null,
    linkFlag: 0,
    subs: [
      {
        icon: null,
        title: '数据解析',
        link: '/data-analysis',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '数据清洗',
        link: '/data-cleaning',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '数据标注',
        link: '/data-annotation',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP, ROLE_KEYS.LABELER],
      },
      {
        icon: null,
        title: '数据增强',
        link: '/data-augmentation',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '数据质检',
        link: '/data-quality-inspection',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP, ROLE_KEYS.LABELER],
      },
      {
        icon: null,
        title: '数据回流',
        link: '/data-reflux',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
    ],
  },
];

// 模型训练
const MODEL_TRAIN = [
  {
    icon: 'iconmoxingxunlian-fill',
    title: '模型训练',
    link: null,
    linkFlag: 0,
    subs: [
      {
        icon: null,
        title: '开发环境',
        link: '/train-dev',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '训练任务',
        link: '/train-task',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
    ],
  },
];

// 模型调优
const MODEL_OPTIMIZE = [
  {
    icon: 'iconmoxingtiaoyou',
    title: '模型调优',
    link: null,
    linkFlag: 0,
    subs: [
      {
        icon: null,
        title: '增量预训练',
        link: '/incremental-pretrain',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '有监督微调',
        link: '/supervised-finetune',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '偏好对齐',
        link: '/preference-alignment',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '模型蒸馏',
        link: '/model-distillation',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '模型压缩',
        link: '/compressManage',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '模型评估',
        link: '/model-evaluation',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
    ],
  },
];

// 模型推理
const MODEL_REASON = [
  {
    icon: 'iconmoxingtuili-fill',
    title: '模型推理',
    link: null,
    linkFlag: 0,
    subs: [
      {
        icon: null,
        title: '在线服务',
        link: '/service-manage',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '应用接入',
        link: '/application-access',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '调用统计',
        link: '/call-statistics',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
    ],
  },
];

// 资产管理
const ASSETS_MANAGEMENT = [
  {
    icon: 'iconzichanguanli-fill',
    title: '资产管理',
    link: null,
    linkFlag: 0,
    subs: [
      {
        icon: null,
        title: '模型管理',
        link: '/modelManage',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '镜像管理',
        link: '/mirrorManage',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '文件管理',
        link: '/file-manage',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '数据集管理',
        link: '/dataset',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
      {
        icon: null,
        title: '提示词管理',
        link: '/prompt-engineering',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER, ROLE_KEYS.DEVELOP],
      },
    ],
  },
];

// 运营管理
const OPERATIONS_MANAGEMENT = [
  {
    icon: 'iconzichanguanli-fill',
    title: '运营运维',
    link: null,
    linkFlag: 0,
    subs: [
      {
        icon: null,
        title: '用量管理',
        link: '/project-usage-manage',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.LEADER],
      },
      {
        icon: null,
        title: '告警管理',
        link: '/project-alarm-manage',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER],
      },
      {
        icon: null,
        title: '操作日志',
        link: '/project-log-manage',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER],
      },
      {
        icon: null,
        title: '语料收集统计',
        link: '/corpus-statistics',
        linkFlag: 0,
        subs: [],
        space: 'project',
        role: [ROLE_KEYS.ADMIN, ROLE_KEYS.LEADER],
      },
    ],
  },
];

// 运管中心
const MANAGEMENT_CENTER = [
  {
    icon: 'icondashboard-fill',
    title: '运管中心',
    link: '',
    linkFlag: 0,
    subs: [
      {
        icon: null,
        title: '运营看板',
        link: '/res-dashboard',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '配额管理',
        link: '/quota-manage',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '用量管理',
        link: '/usage-manage',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '告警管理',
        link: '/alarm-manage',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '语料收集管理',
        link: '/corpus-manage',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '数据安全管理',
        link: '/data-security',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '操作日志',
        link: '/operation-log',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '项目活动管理',
        link: '/activity-manage',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '资源组管理',
        link: '/resource-group',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '预置资产管理',
        link: '/preset-asset',
        space: 'pool',
        linkFlag: 0,
        subs: [],
      },
      {
        icon: null,
        title: '存储目录管理',
        link: '/storage-dir-manager',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '公告管理',
        link: '/announcement',
        linkFlag: 0,
        subs: [],
        space: 'pool',
        role: [],
      },
      {
        icon: null,
        title: '日志管理',
        link: './log-mgr',
        linkFlag: 1, // 跳转到外部地址，通过nginx做重定向
        subs: [],
        space: 'pool',
        role: [],
      },
    ],
  },
];

// 运管平台
const MANAGEMENT_PLATFORM = [
  {
    icon: 'icondashboard-fill',
    title: '运营总览',
    link: '/manage-overview',
    linkFlag: 0,
    subs: [],
    space: 'platform',
    role: [],
  },
  {
    icon: 'icondata',
    title: '资源池管理',
    link: '/resource-pool-manage',
    linkFlag: 0,
    subs: [],
    space: 'platform',
    role: [],
  },
];
export const MENU_DATA = [...HOME, ...PROJECT_SPACE, ...MODEL_SQUARE, ...MODEL_EXPERIENCE, ...DATA_PREPARATION, ...MODEL_TRAIN, ...MODEL_OPTIMIZE, ...MODEL_REASON, ...ASSETS_MANAGEMENT, ...OPERATIONS_MANAGEMENT, ...MANAGEMENT_CENTER, ...MANAGEMENT_PLATFORM];
