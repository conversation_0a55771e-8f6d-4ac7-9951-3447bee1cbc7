import { requestWithProjectId } from '@/request';
const { POST } = requestWithProjectId;

// 查询预置数据集
export const getDatasetPresetList = (data) => POST('/web/dataset/v1/external/preset/query', data);
// 查询分享的自定义数据集版本信息
export const getDatasetShareList = (data) => POST('/web/dataset/v1/external/query/share/versionIds', data);
// 查询非分享的自定义数据集版本信息
export const getDatasetNoShareList = (data) => POST('/web/dataset/v1/external/query/noShare/versionIds', data);
// 自定义数据集查询接口
export const queryDataset = (data) => POST('/web/dataset/v1/external/queryDataset', data);
