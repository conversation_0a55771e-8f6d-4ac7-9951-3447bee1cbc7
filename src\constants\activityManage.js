import { TASK_TYPE as trainTaskType, TASK_TYPE_MSG as trainTaskMsg, tableStatusFilter, TASK_COMMON_STATUS, EVALUATION_TASK_STATUS, DATA_ANALYSIE_STATUS } from '@/constants/trainTask';
import { STATUS_TEXT_MAP, ANALYSIS_STATUS_TEXT } from '@/constants';
import { transformObjectToArray } from '@/utils';

/* 项目活动管理 */
export const TASK_TYPE = {
  ...trainTaskType,
  // 增量预训练、有监督微调、模型压缩、模型评估
  DAP: 'dap',
  SFT: 'sft',
  COMPRESS: 'compress',
  EVALUATION: 'evaluation',
  // 数据清洗、数据解析、数据增强、偏好对齐(v.2.0)
  CLEAN: 'clean',
  ANALYSIS: 'analysis',
  ANGMENTATION: 'angmentation',
  DPO: 'dpo',
  KD: 'kd', // 模型蒸馏（221新增）
};

export const TASK_TYPE_MSG_TABS = {
  ...trainTaskMsg,
  [TASK_TYPE.DAP]: '增量预训练',
  [TASK_TYPE.SFT]: '有监督微调',
  [TASK_TYPE.COMPRESS]: '模型压缩',
  [TASK_TYPE.EVALUATION]: '模型评估',
  // 数据清洗、数据解析、数据增强、偏好对齐(v.2.0)
  [TASK_TYPE.CLEAN]: '数据清洗',
  [TASK_TYPE.ANALYSIS]: '数据解析',
  [TASK_TYPE.ANGMENTATION]: '数据增强',
  [TASK_TYPE.DPO]: '偏好对齐',
  [TASK_TYPE.KD]: '模型蒸馏',
};

export const TASK_TYPE_MSG = {
  ...trainTaskMsg,
  [TASK_TYPE.DAP]: '增量预训练任务',
  [TASK_TYPE.SFT]: '有监督微调任务',
  [TASK_TYPE.COMPRESS]: '模型压缩任务',
  [TASK_TYPE.EVALUATION]: '模型评估任务',
  // 数据清洗、数据解析、数据增强、偏好对齐(v.2.0)
  [TASK_TYPE.CLEAN]: '数据清洗任务',
  [TASK_TYPE.ANALYSIS]: '数据解析任务',
  [TASK_TYPE.ANGMENTATION]: '数据增强任务',
  [TASK_TYPE.DPO]: '偏好对齐任务',
  [TASK_TYPE.KD]: '模型蒸馏任务',
};

/* 详情对应跳转的路由 */
export const DETAIL_ROUTE_MAP = {
  [TASK_TYPE.DEV]: '/activity-manage/dev/detail',
  [TASK_TYPE.TASK]: '/activity-manage/task/detail',
  [TASK_TYPE.INFE]: '/activity-manage/service/detail',
  [TASK_TYPE.DAP]: '/activity-manage/dap/detail',
  [TASK_TYPE.SFT]: '/activity-manage/sft/detail',
  [TASK_TYPE.COMPRESS]: '/activity-manage/compress/detail',
  [TASK_TYPE.EVALUATION]: '/activity-manage/evaluation/detail',
  [TASK_TYPE.CLEAN]: '/activity-manage/clean/detail',
  [TASK_TYPE.ANALYSIS]: '/activity-manage/analysis/detail',
  [TASK_TYPE.ANGMENTATION]: '/activity-manage/angmentation/detail',
  [TASK_TYPE.DPO]: '/activity-manage/dpo/detail',
  [TASK_TYPE.KD]: '/activity-manage/kd/detail',
};

export const SOURCE_TYPE = {
  gpucard: '加速卡',
  cpu: 'CPU',
};

export const SERVICE_SOURCR_TYPE = {
  GPU: '加速卡',
  CPU: 'CPU',
};

const COMMON_TASK_FILTER = [
  {
    text: STATUS_TEXT_MAP.QUEUE,
    value: TASK_COMMON_STATUS.QUEUE,
  },
  {
    text: STATUS_TEXT_MAP.STARTING,
    value: TASK_COMMON_STATUS.onSTART,
  },
  {
    text: STATUS_TEXT_MAP.RUNNING,
    value: TASK_COMMON_STATUS.RUNNING,
  },
  {
    text: STATUS_TEXT_MAP.STOPPING,
    value: TASK_COMMON_STATUS.onSTOP,
  },
  {
    text: STATUS_TEXT_MAP.STOPPED,
    value: TASK_COMMON_STATUS.STOP,
  },
  {
    text: STATUS_TEXT_MAP.SUCCESS,
    value: TASK_COMMON_STATUS.SUCCESS,
  },
  {
    text: STATUS_TEXT_MAP.FAIL,
    value: TASK_COMMON_STATUS.FAIL,
  },
];

const EVALUATION_TASK_FILTER = [
  {
    text: STATUS_TEXT_MAP.QUEUE,
    value: EVALUATION_TASK_STATUS.QUEUE,
  },
  {
    text: STATUS_TEXT_MAP.STARTING,
    value: EVALUATION_TASK_STATUS.onSTART,
  },
  {
    text: STATUS_TEXT_MAP.RUNNING,
    value: EVALUATION_TASK_STATUS.RUNNING,
  },
  {
    text: STATUS_TEXT_MAP.STOPPING,
    value: EVALUATION_TASK_STATUS.onSTOP,
  },
  {
    text: STATUS_TEXT_MAP.STOPPED,
    value: EVALUATION_TASK_STATUS.STOP,
  },
  {
    text: STATUS_TEXT_MAP.SUCCESS,
    value: EVALUATION_TASK_STATUS.SUCCESS,
  },
  {
    text: STATUS_TEXT_MAP.FAIL,
    value: EVALUATION_TASK_STATUS.FAIL,
  },
  {
    text: STATUS_TEXT_MAP.WAITING,
    value: EVALUATION_TASK_STATUS.WAITING,
  },
];

// 数据解析状态筛选
const ANALYSIS_TASK_FILTER = [
  {
    text: ANALYSIS_STATUS_TEXT.RUNNING,
    value: DATA_ANALYSIE_STATUS.RUNNING,
  },
  {
    text: ANALYSIS_STATUS_TEXT.QUEUE,
    value: DATA_ANALYSIE_STATUS.QUEUE,
  },
  {
    text: ANALYSIS_STATUS_TEXT.STOPPING,
    value: DATA_ANALYSIE_STATUS.onSTOP,
  },
  {
    text: ANALYSIS_STATUS_TEXT.STOPPED,
    value: DATA_ANALYSIE_STATUS.STOP,
  },
  {
    text: ANALYSIS_STATUS_TEXT.FINISHED,
    value: DATA_ANALYSIE_STATUS.FINISHED,
  },
  {
    text: ANALYSIS_STATUS_TEXT.FAIL,
    value: DATA_ANALYSIE_STATUS.FAIL,
  },
];

// 数据清洗、增强状态筛选
const DATA_TASK_FILTER = [
  {
    text: STATUS_TEXT_MAP.QUEUE,
    value: TASK_COMMON_STATUS.QUEUE,
  },
  {
    text: STATUS_TEXT_MAP.RUNNING,
    value: TASK_COMMON_STATUS.RUNNING,
  },
  {
    text: STATUS_TEXT_MAP.STOPPED,
    value: TASK_COMMON_STATUS.onSTOP,
  },
  {
    text: STATUS_TEXT_MAP.SUCCESS,
    value: TASK_COMMON_STATUS.SUCCESS,
  },
  {
    text: STATUS_TEXT_MAP.FAIL,
    value: TASK_COMMON_STATUS.FAIL,
  },
];

export const PRIORITY_MAP = {
  high: '高',
  med: '中',
  low: '低',
};

const PRIORITY_FILTERS = [
  {
    text: '高',
    value: 'high',
  },
  {
    text: '中',
    value: 'med',
  },
  {
    text: '低',
    value: 'low',
  },
];

export const TASK_STATUS_MAP = {
  [TASK_TYPE.DAP]: COMMON_TASK_FILTER,
  [TASK_TYPE.SFT]: COMMON_TASK_FILTER,
  [TASK_TYPE.COMPRESS]: COMMON_TASK_FILTER,
  [TASK_TYPE.EVALUATION]: EVALUATION_TASK_FILTER,
  [TASK_TYPE.CLEAN]: DATA_TASK_FILTER,
  [TASK_TYPE.ANALYSIS]: ANALYSIS_TASK_FILTER,
  [TASK_TYPE.ANGMENTATION]: DATA_TASK_FILTER,
  [TASK_TYPE.DPO]: COMMON_TASK_FILTER,
  [TASK_TYPE.KD]: COMMON_TASK_FILTER,
};

export const formatRateValue = (text) => (text === null || text === undefined ? '--' : `${text}%`);

const rateColumnSegment = [
  {
    title: '加速卡使用率',
    dataIndex: 'gpuUsageRate',
    key: 'gpuUsageRate',
    sorter: true,
    width: 130,
    customRender: ({ text }) => {
      return formatRateValue(text);
    },
  },
  {
    title: '加速卡显存使用率',
    dataIndex: 'gpuMemUsageRate',
    key: 'gpuMemUsageRate',
    sorter: true,
    width: 160,
    customRender: ({ text }) => {
      return formatRateValue(text);
    },
  },
  {
    title: 'CPU使用率',
    dataIndex: 'cpuUsageRate',
    key: 'cpuUsageRate',
    sorter: true,
    width: 120,
    customRender: ({ text }) => {
      return formatRateValue(text);
    },
  },
  {
    title: '内存使用率',
    dataIndex: 'memUsageRate',
    key: 'memUsageRate',
    sorter: true,
    width: 120,
    customRender: ({ text }) => {
      return formatRateValue(text);
    },
  },
];

/* table的column定义 */
export const TABLE_COLUMNS = {
  [TASK_TYPE.DEV]: [
    {
      title: '开发环境名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '环境状态',
      dataIndex: 'status',
      key: 'status',
      filters: tableStatusFilter(TASK_TYPE.DEV),
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '最近启动时间',
      dataIndex: 'startTime',
      key: 'startTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.TASK]: [
    {
      title: '训练任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      key: 'status',
      filters: tableStatusFilter(TASK_TYPE.TASK),
      width: 120,
    },
    {
      title: '优先级',
      dataIndex: 'priority',
      key: 'priority',
      filters: PRIORITY_FILTERS,
      customRender: ({ text }) => {
        return PRIORITY_MAP[text] || '--';
      },
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '最近提交时间',
      dataIndex: 'submitTime',
      key: 'submitTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.INFE]: [
    {
      title: '在线服务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SERVICE_SOURCR_TYPE),
      customRender({ text }) {
        return SERVICE_SOURCR_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '服务状态',
      dataIndex: 'status',
      key: 'status',
      filters: tableStatusFilter(TASK_TYPE.INFE),
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      key: 'updateTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.DAP]: [
    {
      title: '增量预训练任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: COMMON_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.SFT]: [
    {
      title: '有监督微调任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: COMMON_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.COMPRESS]: [
    {
      title: '压缩任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: COMMON_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.EVALUATION]: [
    {
      title: '评估任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '评估模型',
      dataIndex: 'modelName',
      key: 'modelName',
      width: '168px',
    },
    {
      title: '资源类型',
      key: 'evaResType',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'evaResGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'evaStatus',
      filters: EVALUATION_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'evaCount',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'evaResourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'subTime',
      key: 'subTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.CLEAN]: [
    {
      title: '数据清洗任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      key: 'status',
      filters: DATA_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      customRender({ text }) {
        return text || '--';
      },
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '提交时间',
      dataIndex: 'startTime',
      key: 'startTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.ANALYSIS]: [
    {
      title: '数据解析任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      key: 'status',
      filters: ANALYSIS_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '提交时间',
      dataIndex: 'subTime',
      key: 'subTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.ANGMENTATION]: [
    {
      title: '数据增强任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      key: 'status',
      filters: DATA_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      customRender({ text }) {
        return text || '--';
      },
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '提交时间',
      dataIndex: 'startTime',
      key: 'startTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.DPO]: [
    {
      title: '偏好对齐任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '任务状态',
      dataIndex: 'status',
      key: 'status',
      filters: COMMON_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
  [TASK_TYPE.KD]: [
    {
      title: '模型蒸馏任务名称',
      dataIndex: 'name',
      key: 'name',
      ellipsis: true,
      fixed: 'left',
      width: 200,
    },
    {
      title: '所属项目空间',
      dataIndex: 'projectName',
      key: 'projectName',
      ellipsis: true,
      width: 140,
    },
    {
      title: '资源类型',
      dataIndex: 'resType',
      filters: transformObjectToArray(SOURCE_TYPE),
      customRender({ text }) {
        return SOURCE_TYPE[text] || '--';
      },
      width: 120,
    },
    {
      title: '所属资源组',
      dataIndex: 'resGroupName',
      key: 'resGroupName',
      filterKey: 'resGroupId',
      filters: [],
      width: 200,
    },
    {
      title: '创建人',
      dataIndex: 'userName',
      key: 'userName',
      width: 140,
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      filters: COMMON_TASK_FILTER,
      width: 120,
    },
    {
      title: '实例数',
      dataIndex: 'count',
      key: 'count',
      sorter: true,
      width: 120,
    },
    ...rateColumnSegment,
    {
      title: '单实例资源配置',
      dataIndex: 'resourceInfo',
      key: 'resourceInfo',
      width: '280px',
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      sorter: true,
      width: '200px',
      customRender: ({ text }) => {
        return text || '--';
      },
    },
  ],
};

export const TASK_TYPE_STATUS_TXT = {
  [TASK_TYPE.DEV]: '环境状态',
  [TASK_TYPE.TASK]: '任务状态',
  [TASK_TYPE.INFE]: '服务状态',
  [TASK_TYPE.DAP]: '增量预训练任务状态',
  [TASK_TYPE.SFT]: '有监督微调任务状态',
  [TASK_TYPE.COMPRESS]: '压缩任务状态',
  [TASK_TYPE.EVALUATION]: '评估任务状态',
  [TASK_TYPE.CLEAN]: '数据清洗状态',
  [TASK_TYPE.ANALYSIS]: '数据解析状态',
  [TASK_TYPE.ANGMENTATION]: '数据增强状态',
  [TASK_TYPE.DPO]: '偏好对齐状态',
  [TASK_TYPE.KD]: '模型蒸馏状态',
};

// 数据清洗-数据类型
export const FILTER_CLEAN_DATA_TYPE = {
  image: '图像',
  prompt_response_pairs: '问答对',
  pure_text: '纯文本',
  multi_rounds_pr_pairs: '多轮问答对',
  prompt_response_pairs_sort: '问答对排序',
  image_text_pr_pairs: '图文问答对',
  image_text_pairs: '图文对',
  image_text_pairs_pn_choice: '图文对-正负选择',
};

// 数据增强-增强类型
export const FILTER_TYPE = {
  text: '文本增强',
  image: '图像增强',
  multimodal: '多模态增强',
};

// 数据增强-数据类型
export const FILTER_DATA_TYPE = {
  image: '图像',
  prompt_response_pairs: '问答对-单轮',
  pure_text: '纯文本',
  multi_rounds_pr_pairs: '问答对',
  image_text_pairs: '图文对',
};

// 数据增强-增强方式
export const FILTER_ENHANCE_MODE = {
  rule_enhance: '规则增强',
  large_model_enhance: '大模型增强',
  prompt_response_pairs_extract: '问答对抽取',
};

// 数据集状态
export const DATASET_DELETE_STATUS = {
  DELETED: 0,
  EXIST: 1,
};

export const DATASET_ENABLE_STATUS = {
  DISENABLE: 0,
  ENABLE: 1,
};

export const DATASET_SHARE_STATUS = {
  SHARE: 0,
  UNSHARE: 1,
};

// 数据解析-任务类型
export const FILTER_TASK_TYPE = {
  0: 'PDF解析',
  1: 'epub解析',
  2: '图像OCR解析',
  3: '图片格式转换',
  4: '视频格式转换',
  5: 'word解析',
  6: '音频格式转换',
  7: 'pdf图片提取',
  8: 'word图片提取',
  9: '视频抽帧',
};
