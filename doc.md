
## 5.2.2 功能描述：SSH远程开发密钥管理

### 1. 功能概述
本功能为项目空间提供SSH密钥的统一管理能力，用于支持安全的**SSH远程开发**。用户能否使用及具体可执行的操作，取决于前置条件（资源池配置、项目空间配置）和用户在项目空间中的角色。

### 2. 前置条件与入口

#### 2.1 显示条件
只有当以下两个条件 **同时满足** 时，用户才可以在项目空间详情页看到此功能的入口：
1.  **资源池已开启** “SSH远程开发”功能。
2.  **项目空间已开启** “数据出口”功能。

若不满足以上条件，则隐藏“SSH远程开发使用方式”相关的所有UI元素及入口。

- **技术说明**：前端可通过 `store.state.projectInfo.sshRemoteAccess` 状态来判断功能是否开启。

#### 2.2 入口位置
在 **项目空间详情页**，功能入口名为“**SSH远程开发使用方式**”，与“高质量数据供给和管理平台使用方式”并列显示。

### 3. 主要交互流程
用户点击“SSH远程开发使用方式”后，系统会弹出信息窗口。窗口内展示的内容和可执行的操作，根据 **“是否已创建密钥”** 和 **“用户角色”** 这两个维度动态变化。

- **通用逻辑**：
    - **获取密钥状态**：弹窗前，需调用接口 `GET /web/project/v1/ssh-key/get` 获取当前项目空间的SSH密钥信息（如指纹、更新时间）或确认密钥是否存在。
    - **动态文案**：弹窗中的引导文案会根据功能模块的显隐状态动态调整。
        - 若 **开发环境** 模块隐藏，文案仅提及“训练任务”。
        - 若 **训练任务** 模块隐藏，文案仅提及“开发环境”。
        - (技术说明：通过 `GET /web/display/v1/sidebar-status` 获取模块显隐配置)

---

#### **场景一：项目空间 `未` 创建SSH密钥**

-   **项目空间负责人**
    -   **界面显示**：提示用户当前未配置SSH密钥，并引导其创建。
    -   **可执行操作**：
        - `[创建密钥]` 按钮（详见 4.1）

-   **项目空间管理员、算法开发人员**
    -   **界面显示**：提示用户当前未配置SSH密钥，并引导其联系项目空间负责人进行创建。
    -   **可执行操作**：无。

---

#### **场景二：项目空间 `已` 创建SSH密钥**

-   **项目空间负责人**
    -   **界面显示**：
        1.  SSH密钥指纹
        2.  密钥更新时间
    -   **可执行操作**：
        - `[下载私钥]` 按钮（详见 4.2）
        - `[重新创建密钥]` 按钮（详见 4.3）
        - `[帮助中心]` 链接（点击后新标签页打开，URL待补充）

-   **项目空间管理员、算法开发人员**
    -   **界面显示**：
        1.  SSH密钥指纹
        2.  密钥更新时间
    -   **可执行操作**：
        - `[下载私钥]` 按钮（详见 4.2）
        - `[帮助中心]` 链接（点击后新标签页打开，URL待补充）

### 4. 核心操作详解

#### 4.1 创建密钥
- **触发条件**：项目空间负责人点击 `[创建密钥]` 按钮。
- **执行流程**：
    1.  调用创建接口 `GET /web/project/v1/ssh-key/create`。
    2.  **创建成功**：
        - 系统自动将生成的 **私钥文件** 下载到用户本地。
        - 顶部全局 `Message` 提示“创建成功”。
        - 弹窗内容自动刷新，切换为 **场景二（已创建密钥）** 的界面，并显示新的密钥指纹和更新时间。
    3.  **创建失败**：
        - 顶部全局 `Message` 提示失败原因。
        - 弹窗内容保持不变，仍为 **场景一（未创建密钥）** 的界面。

#### 4.2 下载私钥
- **触发条件**：有权限的用户（负责人、管理员、开发人员）点击 `[下载私钥]` 按钮。
- **执行流程**：
    1.  弹出 **短信验证码** 对话框。
    2.  用户点击 `[获取验证码]`：
        - 调用接口 `GET /web/project/v1/member/smscode` 向当前用户手机发送验证码。
        - 按钮进入60秒倒计时，期间不可重复点击。
    3.  用户输入验证码，点击 `[确定]`：
        - 调用下载接口 `GET /web/project/v1/ssh-key/download`，并带上验证码参数。
        - **验证通过**：
            - 自动下载私钥文件至本地。
            - 短信验证弹窗关闭，主弹窗内容不变。
        - **验证失败**：
            - 在验证码弹窗内飘红提示错误信息（如“验证码错误”）。
            - 弹窗不关闭，用户可重新输入或获取验证码。

#### 4.3 重新创建密钥
- **触发条件**：项目空间负责人点击 `[重新创建密钥]` 按钮。
- **执行流程**：
    1.  弹出 **二次确认** 对话框，提示“重新创建将导致原有密钥失效，请谨慎操作”。
    2.  用户点击 `[确定]`：
        - 执行与 **4.1 创建密钥** 相同的流程。
        - **成功**：自动下载新私钥，并刷新主弹窗内的密钥指纹和更新时间。
        - **失败**：提示失败原因，主弹窗信息保持不变。
    3.  用户点击 `[取消]`：
        - 关闭二次确认对话框，无任何变更。

### 5. 技术参考

#### 5.1 相关接口（API）
- **获取密钥信息**: `GET /web/project/v1/ssh-key/get`
  - *说明*: 获取SSH公钥指纹和更新时间。
- **创建/重新创建密钥**: `GET /web/project/v1/ssh-key/create`
  - *说明*: 创建一个新的SSH密钥对。
- **下载私钥**: `GET /web/project/v1/ssh-key/download`
  - *说明*: 下载SSH私钥，需要提供短信验证码。
- **发送短信验证码**: `GET /web/project/v1/member/smscode`
  - *说明*: 用于在下载私钥前进行身份验证。
- **获取侧边栏状态**: `GET /web/display/v1/sidebar-status`
  - *说明*: 用于判断“开发环境”、“训练任务”等模块是否对用户可见。

#### 5.2 相关错误码
| 错误码 | 描述信息 | 场景说明 |
| :--- | :--- | :--- |
| `130233` | `generate ssh key fail` | 生成SSH密钥失败 |
| `130234` | `ssh key not exist` | SSH密钥不存在 |
| `130235` | `member phone number is empty` | 用户未绑定手机号，无法发送验证码 |
| `130236` | `send sms exception, please try later` | 短信服务异常，发送失败 |
| `130237` | `verify sms code exception` | 验证码校验服务异常 |
| `998609` | `验证码已过期，请重新获取` | 用户输入的验证码已超过有效期 |
| `998610` | `验证码有误，请重新输入` | 用户输入的验证码错误 |
| `998611` | `验证码发送过于频繁` | 用户在倒计时内重复请求发送验证码 |
| `998612` | `未发送验证码` | 系统检测到未成功发送验证码（内部逻辑） |