export const ALARM_MANAGE_TABS = {
  ALARM_RECORD: '1', // 告警记录
  ALARM_RULES: '2', // 告警规则
  ALARM_NOTICE: '3', // 通知组
};

export const ALARM_MANAGE_SEARCH_PLACEHOLDER = {
  [ALARM_MANAGE_TABS.ALARM_RECORD]: '告警单号/监控对象/所属告警规则/通知对象',
  [ALARM_MANAGE_TABS.ALARM_RULES]: '规则名称/监控对象/告警事件',
  [ALARM_MANAGE_TABS.ALARM_NOTICE]: '通知组名称/成员/描述/创建人',
};

export const ALARM_MANAGE_BUTTON_TEXT = {
  [ALARM_MANAGE_TABS.ALARM_RULES]: '新建规则',
  [ALARM_MANAGE_TABS.ALARM_NOTICE]: '新建通知组',
};

// @Iteration: [v2.0.1] 告警事件
export const ALARM_INCIDENT_MAP = {
  TASK_INTERRUPT: '0', // 训练任务中断
  TASK_RETRY: '1', // 训练任务重试
  STORAGE_FULL: '2', // 对象存储占满
  FILE_FULL: '3', // 普通文件存储占满
  HIGH_FILE_FULL: '4', // 高性能文件存储占满
};
export const ALARM_INCIDENT_LIST = [
  { text: '训练任务中断', value: ALARM_INCIDENT_MAP.TASK_INTERRUPT },
  { text: '训练任务重试', value: ALARM_INCIDENT_MAP.TASK_RETRY },
  { text: '对象存储占满', value: ALARM_INCIDENT_MAP.STORAGE_FULL },
  { text: '普通文件存储占满', value: ALARM_INCIDENT_MAP.FILE_FULL },
  { text: '高性能文件存储占满', value: ALARM_INCIDENT_MAP.HIGH_FILE_FULL },
];

// @Iteration: [v2.0.1] 监控模块
export const MONITORING_MODULES_MAP = {
  PROJECT_SPACE: '0', // 项目空间
  INFERENCE_SRVICE: '1', // 在线服务
  TRAINING_TASK: '2', // 训练任务
};
export const MONITORING_MODULES_LIST = [
  { text: '项目空间', value: MONITORING_MODULES_MAP.PROJECT_SPACE },
  { text: '在线服务', value: MONITORING_MODULES_MAP.INFERENCE_SRVICE },
  { text: '训练任务', value: MONITORING_MODULES_MAP.TRAINING_TASK },
];

// @Iteration: [v2.0.1] 事件等级
export const EVENT_LEVEL_MAP = {
  URGENT: '1', // 紧急
  IMPORTS: '2', // 重要
  MINOR: '3', // 次要
  info: '4', // 提示
};
export const EVENT_LEVEL_COLOR_MAP = {
  [EVENT_LEVEL_MAP.IMPORTS]: 'tag-alarmorange-jt',
  [EVENT_LEVEL_MAP.URGENT]: 'tag-alarmred-jt',
  [EVENT_LEVEL_MAP.MINOR]: 'tag-alarmblue-jt',
  [EVENT_LEVEL_MAP.info]: 'tag-alarmgreen-jt',
};
export const EVENT_LEVEL_LIST = [
  { text: '紧急', value: EVENT_LEVEL_MAP.URGENT },
  { text: '重要', value: EVENT_LEVEL_MAP.IMPORTS },
  { text: '次要', value: EVENT_LEVEL_MAP.MINOR },
  { text: '提示', value: EVENT_LEVEL_MAP.info },
];

// @Iteration: [v2.0.1] 通知方式
export const NOTICE_TYPE_MAP = {
  NOTE: '1', // 短信
  EMAIL: '2', // 邮件
  MESSAGE: '3', // 站内信
};
export const NOTICE_TYPE_LIST = [
  { label: '发送短信', text: '发送短信', value: NOTICE_TYPE_MAP.NOTE },
  { label: '发送邮件', text: '发送邮件', value: NOTICE_TYPE_MAP.EMAIL },
  { label: '发送站内信', text: '发送站内信', value: NOTICE_TYPE_MAP.MESSAGE },
];

// @Iteration: [v2.0.1] 监控对象
export const MONITORIN_OBJECT_MAP = {
  STORE_MIRROR: '0', // 存储镜像资源
  INFERENCE_SERVICE: '1', // 全部在线服务
  ASSIGN_TRAINING: '2', // 指定训练任务
  RUN_DEVELOP: '3', // 运行开发环境
};
export const MONITORIN_OBJECT_LIST = [
  { text: '存储镜像资源', value: MONITORIN_OBJECT_MAP.STORE_MIRROR },
  { text: '全部在线服务', value: MONITORIN_OBJECT_MAP.INFERENCE_SERVICE },
  { text: '指定训练任务', value: MONITORIN_OBJECT_MAP.ASSIGN_TRAINING },
  { text: '运行开发环境', value: MONITORIN_OBJECT_MAP.RUN_DEVELOP },
];

// @Iteration: [v2.0.1] 启用状态
export const STATUS_ALARM_MAP = {
  CLOSE: 0, // 关闭
  START: 1, // 开启
};
export const STATUS_ALARM_LIST = [
  { text: '关闭', value: STATUS_ALARM_MAP.CLOSE },
  { text: '开启', value: STATUS_ALARM_MAP.START },
];

// @Iteration: [v2.0.1] 通知状态
export const NOTICE_STATUS_MAP = {
  NO_SEND: '0', // 未发送
  SUCCESS: '1', // 发送成功
  FAILED: '2', // 发送失败
  NO_NEED: '3', // 不需要发送 - 未配置
};

// @Iteration: [v2.0.1] 触发方式
export const TRIGGER_TYPE_MAP = {
  NOW: '1', // 立即告警
  // WAIT: '2', // 累计告警
};
export const TRIGGER_TYPE_LIST = [
  { text: '立即告警', value: TRIGGER_TYPE_MAP.NOW },
  // { text: '累计告警', value: TRIGGER_TYPE_MAP.WAIT },
];

// @Iteration: [v2.0.1] 触发模式
export const TRIGGER_MODE_MAP = {
  ONCE: '1', // 只告警一次
};
export const TRIGGER_MODE_LIST = [{ text: '只告警一次', value: TRIGGER_MODE_MAP.ONCE }];

// @Iteration: [v2.0.1] 通知状态
export const NOTICE_SESSTING_STATUS_MAP = {
  CLOSE: 0, // 关闭
  OPEN: 1, // 开启
};
export const NOTICE_STATUS_COLOR_MAP = {
  [NOTICE_SESSTING_STATUS_MAP.CLOSE]: 'default', // 关闭
  [NOTICE_SESSTING_STATUS_MAP.OPEN]: 'green', // 开启
};
export const NOTICE_SESSTING_STATUS_LIST = [
  { text: '开启', value: NOTICE_SESSTING_STATUS_MAP.OPEN },
  { text: '关闭', value: NOTICE_SESSTING_STATUS_MAP.CLOSE },
];

export const ALARM_VIEW_AUTH = { type: 'mgt', key: 'alert', value: 'view' };
export const ALARM_EDIT_AUTH = { type: 'mgt', key: 'alert', value: 'edit' };
