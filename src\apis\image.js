import { requestWithProjectId } from '@/request';
const { GET, POST } = requestWithProjectId;

// 保存镜像
export const saveImage = (data) => POST('/web/image/v1/custom_image/save', data);

// 预制镜像版本清单
export const getPresetImages = (data) => POST('/web/image/v1/preset_image/preset_image_list', data);

// 自定义镜像清单
export const getCustomImageGroups = (data) => POST('/web/image/v1/custom_image/custom_image_list', data);

// 自定义镜像版本清单
export const getCustomImageVersions = (data) => GET('/web/image/v1/dev_env/manage/custom_image_versions', data);

// 镜像名称模糊匹配
export const matchImages = (data) => POST('/web/image/v1/dev_env/manage/match', data);

// 获取镜像最大版本
export const getMaxImageVersion = (data) => GET('web/image/v1/dev_env/manage/generate_version_num', data);

// 根据ID获取镜像
export const getImageById = (data) => GET('/web/image/v1/dev_env/manage/get_image', data);

// 获取镜像剩余资源
export const getImageRemainStorage = (data) => GET('/web/image/v1/dev_env/manage/remaining_capacity', data);
