import dayjs from 'dayjs';
import { resourceApi } from '@/apis';
import ErrorCode from '@/constants/errorCode';
import { STATUS_TEXT_MAP, INSTATUS_TEXT_MAP } from '@/constants/index';
export const TASK_TYPE = {
  // DEV: 'develop',
  // TASK: 'task',
  DEV: 'inc-pretrain',
  TASK: 'sft',
  INFE: 'serving',
};

export const TASK_TYPE_MSG = {
  [TASK_TYPE.DEV]: '增量预训练',
  [TASK_TYPE.TASK]: '有监督微调',
  [TASK_TYPE.INFE]: '推理服务',
};

// 资源组类型
export const RESOURCE_TYPE = {
  CPU: 'cpu', // CPU
  GPU: 'gpucard', // 加速卡
};
export const RESOURCE_TYPE_MSG = {
  [RESOURCE_TYPE.CPU]: 'CPU',
  [RESOURCE_TYPE.GPU]: '加速卡',
};
// 资源组大类（公共资源组、专属资源组）
export const RESOURCE_GROUP_TYPE = {
  PUBLIC: 'public', // 公共资源组
  PERSONAL: 'personal', // 专属资源组
};

// 任务状态表
export const STATUS = {
  [TASK_TYPE.DEV]: {
    DELETED: -1, //已删除
    STARTING: 1, //正在启动
    WAITING: 2, //排队中
    RUNNING: 3, //运行
    SUCCEED: 4, //成功
    STOPPING: 5, //正在停止
    FAILED: 6, //失败
    STOPPED: 7, //已停止
  },
  [TASK_TYPE.TASK]: {
    DELETED: -1, //已删除
    STARTING: 1, //正在启动
    WAITING: 2, //排队中
    RUNNING: 3, //运行
    SUCCEED: 4, //成功
    STOPPING: 5, //正在停止
    FAILED: 6, //失败
    STOPPED: 7, //已停止
  },
  [TASK_TYPE.INFE]: {
    onSTART: 'starting', // 启动中
    UPDATING: 'updating', // 更新中
    RUNNING: 'running', // 运行中
    onSTOP: 'stopping', // 停止中
    STOP: 'stopped', // 已停止
    FAIL: 'error', // 失败
    PARTIALLYRUNNING: 'partiallyRunning', // 部分运行中
  },
};

export const getTrainStatusMarkStyle = (type, status) => {
  const statusClassnamemap = new Map([
    [STATUS[type]?.QUEUE, 'warning'],
    [STATUS[type]?.QUEUE, 'warning'],
    [STATUS[type]?.onSTART, 'warning'],
    [STATUS[type]?.onSTOP, 'warning'],
    [STATUS[type]?.STARTED, 'complete'],
    [STATUS[type]?.RUNNING, 'processing'],
    [STATUS[type]?.FAIL, 'error'],
    [STATUS[type]?.STOP, 'default'],
    //以下2种暂无用
    [STATUS[type]?.SNAPSHOT, 'warning'],
    [STATUS[type]?.LOCKED, 'warning'],
  ]);
  const getStatus = isNaN(+status) ? status : +status;
  if (!statusClassnamemap.has(getStatus)) {
    return getStatus;
  }
  return statusClassnamemap.get(getStatus);
};

export const getTrainStatusMarkWord = (type, status) => {
  const statusWordmap = new Map([
    //已删除
    [STATUS[type].DELETED, INSTATUS_TEXT_MAP.DELETED],
    // 排队中
    [STATUS[type].WAITING, INSTATUS_TEXT_MAP.WAITING],
    // 启动中
    [STATUS[type].STARTING, INSTATUS_TEXT_MAP.STARTING],
    // 停止中
    [STATUS[type].STOPPING, INSTATUS_TEXT_MAP.STOPPING],
    // 运行中
    [STATUS[type].RUNNING, INSTATUS_TEXT_MAP.RUNNING],
    // 失败
    [STATUS[type].FAILED, INSTATUS_TEXT_MAP.FAILED],
    // 已停止
    [STATUS[type].STOPPED, INSTATUS_TEXT_MAP.STOPPED],
    // 成功
    [STATUS[type].SUCCEED, INSTATUS_TEXT_MAP.SUCCEED],
  ]);
  const getStatus = isNaN(+status) ? status : +status;
  if (!statusWordmap.has(getStatus)) {
    return getStatus;
  }
  return statusWordmap.get(getStatus);
};

export const tableStatusFilter = (type) => {
  if (type === TASK_TYPE.DEV) {
    return [
      // 运行中
      {
        text: INSTATUS_TEXT_MAP.RUNNING,
        value: STATUS[TASK_TYPE.DEV].RUNNING,
      },
      //启动中
      {
        text: INSTATUS_TEXT_MAP.STARTING,
        value: STATUS[TASK_TYPE.DEV].STARTING,
      },
      //排队中
      {
        text: INSTATUS_TEXT_MAP.WAITING,
        value: STATUS[TASK_TYPE.DEV].WAITING,
      },
      //停止中
      {
        text: INSTATUS_TEXT_MAP.STOPPING,
        value: STATUS[TASK_TYPE.DEV].STOPPING,
      },
      // 已停止
      {
        text: INSTATUS_TEXT_MAP.STOPPED,
        value: STATUS[TASK_TYPE.DEV].STOPPED,
      },
      //失败
      {
        text: INSTATUS_TEXT_MAP.FAILED,
        value: STATUS[TASK_TYPE.DEV].FAILED,
      },
      //成功
      {
        text: INSTATUS_TEXT_MAP.SUCCEED,
        value: STATUS[TASK_TYPE.DEV].SUCCEED,
      },
      // 已删除
      {
        text: INSTATUS_TEXT_MAP.DELETED,
        value: STATUS[TASK_TYPE.DEV].DELETED,
      },
    ];
  }
  if (type === TASK_TYPE.TASK) {
    return [
      {
        text: INSTATUS_TEXT_MAP.RUNNING,
        value: STATUS[TASK_TYPE.TASK].RUNNING,
      },
      {
        text: INSTATUS_TEXT_MAP.STARTING,
        value: STATUS[TASK_TYPE.TASK].onSTART,
      },
      {
        text: INSTATUS_TEXT_MAP.QUEUE,
        value: STATUS[TASK_TYPE.TASK].QUEUE,
      },
      {
        text: INSTATUS_TEXT_MAP.STOPPING,
        value: STATUS[TASK_TYPE.TASK].onSTOP,
      },
      {
        text: INSTATUS_TEXT_MAP.STOPPED,
        value: STATUS[TASK_TYPE.TASK].STOP,
      },
      {
        text: INSTATUS_TEXT_MAP.SUCCESS,
        value: STATUS[TASK_TYPE.TASK].STARTED,
      },
      {
        text: INSTATUS_TEXT_MAP.FAIL,
        value: STATUS[TASK_TYPE.TASK].FAIL,
      },
    ];
  }
  if (type === TASK_TYPE.INFE) {
    return [
      {
        text: INSTATUS_TEXT_MAP.STARTING,
        value: STATUS[TASK_TYPE.INFE].onSTART,
      },
      {
        text: INSTATUS_TEXT_MAP.PARTIALLYRUNNING,
        value: STATUS[TASK_TYPE.INFE].PARTIALLYRUNNING,
      },
      {
        text: INSTATUS_TEXT_MAP.RUNNING,
        value: STATUS[TASK_TYPE.INFE].RUNNING,
      },
      {
        text: INSTATUS_TEXT_MAP.STOPPING,
        value: STATUS[TASK_TYPE.INFE].onSTOP,
      },
      {
        text: INSTATUS_TEXT_MAP.STOPPED,
        value: STATUS[TASK_TYPE.INFE].STOP,
      },
      {
        text: INSTATUS_TEXT_MAP.RETRYING,
        value: STATUS[TASK_TYPE.INFE].FAIL,
      },
      {
        text: INSTATUS_TEXT_MAP.UPDATING,
        value: STATUS[TASK_TYPE.INFE].UPDATING,
      },
    ];
  }
};

// 表格 单实例资源配置 列内容显示
export const getResourceInfoWord = (value, taskType) => {
  let textArray = [];
  if (taskType !== TASK_TYPE.INFE) {
    if (+value.gpu) {
      textArray.push(`${value.gpu}加速卡`);
    }
    textArray.push(`${value.cpu ? `${value.cpu}核CPU` : ''}`);
    textArray.push(`${value.mem ? `${value.mem}GB内存` : ''}`);
  } else {
    if (+value.gpu) {
      textArray.push(`${value.gpu}加速卡`);
    }
    textArray.push(`${value.cpu ? `${value.cpu / 1000}核CPU` : ''}`);
    textArray.push(`${value.memory ? `${value.memory / 1024}GB内存` : ''}`);
  }
  return textArray;
};

export const DEFAULT_DISPLAY_RULE = {
  optionsBtn: {
    run: true,
    stop: true,
    copy: true,
    delete: true,
    publish: true,
  },
};

export const TRAIN_TYPE_DISPLAY_RULE = {
  [TASK_TYPE.DEV]: {
    ...DEFAULT_DISPLAY_RULE,
  },
  [TASK_TYPE.TASK]: {
    ...DEFAULT_DISPLAY_RULE,
  },
};

export function btnDisabled(type, record, taskType) {
  let disabled = false;
  // 训练成功是否已超过30天
  const endTimeStamp = dayjs(record?.endTime).valueOf();
  const last30Days = dayjs(dayjs().valueOf()).subtract(30, 'day').valueOf(); //距离现在30天前的时间戳

  const status = isNaN(+record.status) ? +record.state : +record.status;
  switch (type) {
    case 'run':
      disabled = [STATUS[taskType].STARTING].includes(status);
      break;
    case 'stop':
      disabled = [STATUS[taskType].STOPPING, STATUS[taskType].SUCCEED].includes(status);
      break;
    case 'delete':
      disabled = false;
      break;
    case 'copy':
      disabled = false;
      break;
    case 'publish':
      disabled = endTimeStamp < last30Days;
      break;
  }
  return disabled;
}

export function showBtn(type, record, taskType) {
  let show = false;
  const status = isNaN(+record.status) ? +record.state : +record.status;

  switch (type) {
    case 'run':
      show = [STATUS[taskType].STOPPED, STATUS[taskType].FAILED].includes(status);
      break;
    case 'stop':
      show = [STATUS[taskType].RUNNING, STATUS[taskType].WAITING].includes(status);
      break;
    case 'copy':
      show = TRAIN_TYPE_DISPLAY_RULE[taskType].optionsBtn.copy;
      break;
    case 'delete':
      show = TRAIN_TYPE_DISPLAY_RULE[taskType].optionsBtn.delete;
      break;
    case 'publish':
      show = [STATUS[taskType].SUCCEED].includes(status);
      break;
  }
  return show;
}

export function taskApiInfo(type) {
  if (type === TASK_TYPE.INFE) {
    return {
      list: `/web/serving/v1/instance/list`, // 服务列表
    };
  }
  return {
    list: `/web/finetune/v1/${type}/task/list`, //增量预训练列表
    start: `/web/finetune/v1/${type}/task/start`, // 运行预训练任务
    stop: `/web/finetune/v1/${type}/task/stop`, // 停止预训练任务
    delete: ` /web/finetune/v1/${type}/task/delete`, // 删除预训练任务

    detail: `/web/${type}/v1/detail`, // 训练任务详情
  };
}

export const getFormatString = (_date) => {
  const date = new Date(_date);
  const H = date.getHours() < 10 ? '0' + date.getHours() : date.getHours();
  const M = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes();
  const S = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds();
  return `${H}:${M}:${S}`;
};

export function changeUseTime(useTime) {
  if (!/:/g.test(useTime)) {
    return useTime;
  }
  const itemTime = useTime?.split(':');
  let [h, m, s] = [Number(itemTime?.[0]), Number(itemTime?.[1]), Number(itemTime?.[2])];
  s++;
  if (s >= 60) {
    s = 0;
    m++;
  }
  if (m >= 60) {
    m = 0;
    h++;
  }
  const timeTxt = `${h < 10 ? '0' + h : h}:${m < 10 ? '0' + m : m}:${s < 10 ? '0' + s : s}`;
  return timeTxt;
}

/**
 * 列表的启动时长变更
 * @param {array} list 变更列表
 */
export const changeTableListUseTime = (list, taskType) => {
  for (const i in list) {
    const item = list[i];
    if (item.status == STATUS[taskType].RUNNING) {
      list[i].useTime = changeUseTime(item.useTime);
    }
  }
  return new Promise((resolve) => {
    resolve(list);
  });
};

export const getResourceGroupList = async () => {
  let resourceGroupList = [];
  try {
    const publicResourceGroupList = await resourceApi.getListAllResourceGroup();
    if (publicResourceGroupList.code === 0) {
      resourceGroupList = resourceGroupList.concat(publicResourceGroupList.data);
    }
  } catch (err) {
    throw new Error(err);
  }
  return Promise.resolve(resourceGroupList);
};

/**
 * 根据错误码返回对应文案（除所需文案之外的错误码返回默认文案）
 * @param {string} code 错误码
 * @returns 错误码对应文案
 */
export const trainTaskErrorMsg = (code) => {
  const devCodeList = [170014, 170015, 170016, 170017, 170025, 170036, 170037, 170038];
  const taskCodeList = [151009, 151010, 151011];
  const ableShowErrorCodeList = [...devCodeList, ...taskCodeList];
  if (ableShowErrorCodeList.includes(code)) {
    return ErrorCode[code];
  } else {
    return '请稍后再试';
  }
};

export const tagColor = (status) => {
  const statusColorMap = {
    运行中: 'green',
    启动中: 'orange',
    停止中: 'orange',
    已停止: 'default',
    成功: 'cyan',
    失败: 'red',
    重试中: 'red',
    排队中: 'quete-yellow-jt',
    快照中: 'orange',
    已锁定: 'default',
    部分运行中: 'green',
  };
  if (status && statusColorMap[status]) {
    return statusColorMap[status];
  }
  return color;
};
