<template>
  <div class="tags-input">
    <div class="input-box" :class="{ 'error-box': isError }">
      <jt-icon type="iconsousuo" class="search-icon" />
      <a-select v-model:value="valueArr" mode="tags" max-tag-count="responsive" placeholder="请输入日志关键词" :get-popup-container="(triggerNode) => triggerNode.parentNode" @deselect="handleDeselect" @inputKeyDown="onInputKeyDown" @search="searchOption"></a-select>
    </div>
    <div v-if="isError" class="error-text">
      <span>{{ errorText }}</span>
    </div>
  </div>
</template>

<script setup>
import { logSearchWordRegex } from '@/constants/regex';
const valueArr = ref([]);
const valueOldArr = ref([]);
const emit = defineEmits(['change']);
const isError = ref(false);
const errorText = ref('');

// 输入的关键词校验函数
const validate = (value) => {
  let canAdd = true;
  const strAll = valueArr.value.reduce(
    (accumulator, currentValue) => {
      if (!accumulator.includes(currentValue)) {
        return accumulator.concat(currentValue);
      } else {
        return accumulator;
      }
    },
    [value]
  );
  const onlyHasSpace = value && value.trim().length === 0;
  const keywords = strAll.reduce((pre, cur) => pre + cur);
  if (logSearchWordRegex.test(keywords) || onlyHasSpace || keywords?.length > 100) {
    canAdd = false;
    isError.value = true;
    errorText.value = logSearchWordRegex.test(keywords) ? '不支持输入特殊正则表达式字符，“$, (), *, +, ., ?, , ^, {}, |”' : onlyHasSpace ? '不支持输入纯空格' : '不支持超出100个字符';
    return canAdd;
  }
  isError.value = false;
  errorText.value = '';
  return canAdd;
};

// 反选
const handleDeselect = () => {
  validate('');
  emit('change', valueArr.value, isError.value);
};

// 回车处理
const onInputKeyDown = (e) => {
  if (!e.target.value) {
    e.stopPropagation();
  }
  if (e.key === 'Enter' && e.target.value) {
    const currentValue = e.target.value;
    const findValue = valueOldArr.value.indexOf(currentValue);
    if (!validate(currentValue)) {
      emit('change', valueArr.value, isError.value);
      e.stopPropagation();
    }
    setTimeout(() => {
      if (!validate(currentValue)) {
        emit('change', valueArr.value, isError.value);
        return;
      }
      if (findValue !== -1) {
        valueArr.value = Array.from(new Set(toRaw(valueArr.value).concat([currentValue])));
      } else {
        valueOldArr.value = Array.from(new Set(toRaw(valueOldArr.value).concat([currentValue])));
      }
    });
  }
};

// 输入关键词变化时的处理
const searchOption = (value) => {
  if (!validate(value)) {
    emit('change', valueArr.value, isError.value);
    return;
  }
};

watch(
  () => valueArr,
  (newValue) => {
    if (!newValue.value?.length) {
      isError.value = false;
      errorText.value = '';
    }
    emit('change', newValue.value, isError.value);
  },
  {
    deep: true,
  }
);

const clearSearchValue = () => {
  valueArr.value = [];
};

defineExpose({
  clearSearchValue,
});
</script>

<style scoped lang="less">
.tags-input {
  .input-box {
    border-radius: 2px;
    position: relative;
    border: 1px solid rgba(0, 20, 26, 0.15);
    min-height: 32px;
    display: inline-block;
    width: 100%;
    box-sizing: border-box;
  }
  .search-icon {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    left: 8px;
    font-size: @jt-font-size-lg;
    color: #aaacb4;
    margin-right: 8px;
    z-index: 1;
  }
  :deep(.ant-select) {
    width: 100%;
    .ant-select-selector {
      border: none !important;
      min-height: 30px !important;
      height: auto !important;
      padding-bottom: 0 !important;
    }
  }
  :deep(.ant-select:not(.ant-select-disabled):not(.ant-select-customize-input):not(.ant-pagination-size-changer):hover .ant-select-selector) {
    border-color: transparent;
    box-shadow: none;
  }
  .error-text {
    position: absolute;
    font-size: @jt-font-size-base;
    color: @jt-error-color;
    margin-top: 4px;
  }
  .error-box {
    border: 1px solid #f53922;
  }
  :deep(.ant-select-multiple .ant-select-selection-overflow-item-suffix) {
    top: 2px !important;
  }
  :deep(.ant-select-multiple .ant-select-selection-placeholder) {
    inset-inline-start: 29px;
  }
  :deep(.ant-select-multiple .ant-select-selector) {
    padding: 1px 4px 1px 22px;
    min-height: 30px !important;
    height: auto !important;
  }
  :deep(.ant-select-multiple .ant-select-selection-overflow-item:nth-child(1) .ant-select-selection-item:nth-child(1)) {
    margin-left: 8px;
  }
  :deep(.ant-select-multiple .ant-select-selection-item) {
    color: @jt-text-color-primary-opacity07;
    background: rgba(0, 20, 26, 0.04);
  }
}
</style>
