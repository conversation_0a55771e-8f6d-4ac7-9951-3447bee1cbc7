import Axios from 'axios';
import notification from '@/utils/notification';

import { getProxyPrefix, portalPrefixUrl } from '@/config';
const proxyPrefix = getProxyPrefix();

const isDev = process.env.NODE_ENV === 'development';
//测试环境特殊处理： 由于测试环境的域名和门户不一致，且是https协议，直接请求 xxx/kunlun 再通过nginx代理访问门户的资源
const isTestEnv = !!process.env.VUE_APP_PORTAL_ROUTE_PREFIX;

// 请求公共组件的接口统一使用portal前缀
const baseURL = isDev ? `/api/${proxyPrefix}` : isTestEnv ? '/kunlun/' : `${portalPrefixUrl}/`;

export const axios = Axios.create({
  baseURL: baseURL,
  withCredentials: true, // set cross-origin
  headers: {
    'Content-Type': 'application/json',
  },
});

axios.interceptors.response.use(
  (response) => {
    return response;
  },
  (err) => {
    if (err.response.status === 401) {
      throw new Error('未登录');
    } else if (err.response.status === 403) {
      notification.error({
        message: `无权限`,
        description: '抱歉，无操作权限',
      });
      throw err;
    } else {
      isDev &&
        notification.error({
          message: `系统错误`,
          description: err.response.config.url,
        });
      throw err;
    }
  }
);

function request(url, options) {
  const { method = 'GET', data = {}, useError = false, headers = {} } = options || {};
  return axios({
    url,
    method,
    params: method === 'GET' ? data : undefined,
    data: ['PUT', 'POST', 'DELETE'].includes(method) ? data : undefined,
    headers: {
      ...headers,
    },
  })
    .then((res) => {
      if (isDev && useError && (res.data.errorCode === '-500' || res.data.errorCode === '-503')) {
        notification.error({
          message: '系统错误',
          description: `${res.config.url}: ${res.data.errorMessage}`,
        });
        throw new Error(res.data);
      }
      return res.data;
    })
    .catch((err) => {
      throw err;
    });
}

export function POST(url, data, options = {}) {
  return request(url, { method: 'POST', data, useError: true, headers: options.headers });
}
export function GET(url, data, options = {}) {
  return request(url, { method: 'GET', data, useError: true, headers: options.headers });
}

export function requestBlob(url, options) {
  const { method = 'GET', data = {}, useError = true, returnType = 'url' } = options;
  return axios({
    url,
    method,
    params: method === 'GET' ? data : undefined,
    data: ['PUT', 'POST', 'DELETE'].includes(method) ? data : undefined,
    responseType: 'blob',
  })
    .then((res) => {
      if (isDev && useError && (res.data.errorCode === '-500' || res.data.errorCode === '-503')) {
        notification.error({
          message: '系统错误',
          description: `${res.config.url}: ${res.data.errorMessage}`,
        });
        throw new Error(res.data);
      }

      return returnType === 'url' ? URL.createObjectURL(new Blob([res.data])) : res.data;
    })
    .catch((err) => {
      throw err;
    });
}

export default request;
