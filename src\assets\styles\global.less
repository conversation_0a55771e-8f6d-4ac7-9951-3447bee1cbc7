/* 全局样式定义 */

// table 状态按钮
.dot-common {
  display: flex;
  align-items: center;
  &:before {
    content: '';
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 5px;
  }
  &.dot-success {
    &:before {
      background-color: #1dca94;
    }
  }
  &.dot-queue {
    &:before {
      background-color: #ff931d;
    }
  }
  &.dot-fail {
    &:before {
      background-color: #ff454d;
    }
  }
  &.dot-running {
    &:before {
      background-color: #0082ff;
    }
  }
  &.dot-wait {
    &:before {
      background-color: #c2c5cf;
    }
  }
}
// table 操作列按钮
.btn-common {
  cursor: pointer;
  color: @jt-primary-color;
  &:hover {
    color: @jt-primary-color;
  }
  &.error {
    cursor: no-drop;
    color: #d8d8d8;
  }
}

.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}

.markdown-body {
  /* table 样式 */
  table {
    border-top: 1px solid #ccc;
    border-left: 1px solid #ccc;
  }
  table td,
  table th {
    border-bottom: 1px solid #ccc;
    border-right: 1px solid #ccc;
    padding: 3px 5px;
  }
  table th {
    border-bottom: 2px solid #ccc;
    text-align: center;
  }

  /* blockquote 样式 */
  blockquote {
    display: block;
    border-left: 8px solid #d0e5f2;
    padding: 5px 10px;
    margin: 10px 0;
    line-height: 1.4;
    font-size: 100%;
    background-color: #f1f1f1;
  }

  /* code 样式 */
  code {
    display: inline-block;
    *display: inline;
    *zoom: 1;
    background-color: #f1f1f1;
    border-radius: 3px;
    padding: 3px 5px;
    margin: 0 3px;
  }
  pre code {
    display: block;
  }

  /* ul ol 样式 */
  ul,
  ol {
    margin: 10px 0 10px 20px;
    padding-inline-start: 0px;
  }

  ul li {
    list-style: disc;
  }
  ol li {
    list-style: decimal;
  }
}

.sub-title-item {
  font-weight: 600;
  color: #00141a;
  line-height: 14px;
  font-size: 14px;
  border-left: 4px solid #00a0cc;
  padding-left: 8px;
  margin-bottom: 20px;
}

.overflow-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.ellipsis-text {
  display: inline-block;
  max-width: 100%;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  &.hover {
    cursor: pointer;
    &:hover {
      color: @jt-primary-color;
    }
  }
}
.delete-text{
  color: rgba(0,20,26,0.25);
  text-decoration-line: line-through;
}
.ant-table-cell .ellipsis-text {
  vertical-align: middle;
}
.ant-table-content .ant-table-thead {
  tr th {
    background-color: rgba(0, 20, 26, 0.02);
  }
}
// 设置table的筛选icon紧贴头部文案
.ant-table-thead {
  th {
    .ant-table-filter-column {
      justify-content: left;
      .ant-table-column-title {
        flex: none;
      }
    }
  }
}
.ant-table-content .ant-table-tbody .ant-table-row:hover td {
  background: #f5faff;
}
// 排序按钮紧贴文案
.ant-table .ant-table-column-sorters {
  justify-content: left;
  .ant-table-column-title {
    white-space: nowrap;
    display: inline-block;
    flex: 0;
  }
}
// table选择列（单选、复选）距离最左侧距离增加
.ant-table-cell.ant-table-selection-column {
  padding-left: 16px !important;
}
// 确认弹窗的统一样式
.confirm-modal {
  padding: 8px 8px 0 9px;
  .modal-title {
    font-weight: 600;
    color: #121f2c;
    margin-bottom: 16px;
    .title-icon {
      font-size: 18px;
      color: #0068ff;
      margin-right: 9px;
    }
    .danger {
      color: red;
    }
  }
  .modal-content {
    font-size: 12px;
    color: #606972;
    padding: 0 0 24px 27px;
    p {
      margin-bottom: 12px;
    }
  }
  .modal-footer {
    display: flex;
    justify-content: right;
  }
}
// table中的loading的最小高度
.ant-table-wrapper {
  &.min-height-table {
    .ant-spin-nested-loading {
      min-height: 500px;
    }
  }
}
.app-loading-spin {
  .ant-spin.ant-spin-spinning {
    height: 100%;
    max-height: 100%;
  }
}
.card-modal .ant-modal-content {
  p {
    margin-top: 16px;
  }
}
//加速卡、CPU使用详情
.gpu-resource-tooltip-wrapper {
  .ant-tooltip-arrow::before {
    background: #fff;
  }
  .ant-tooltip-inner {
    width: 238px;
    height: 98px;
    padding: 12px;
    color: #00141a;
    background: #fff;
    .info-title {
      font-size: 14px;
      font-weight: 600;
      .typespan {
        color: rgba(0, 20, 26, 0.7);
        .font-color {
          color: rgba(0, 20, 26, 1);
          font-weight: 700;
        }
      }
    }
  }
}

// 抽屉
// 抽屉的关闭按钮放在右上角
.ant-drawer-header-title {
  margin: 0;
  flex-direction: row-reverse;
  // 抽屉标题颜色调整
  .ant-drawer-title {
    color: @jt-text-color-primary;
  }
}
// 抽屉组件右上角的关闭按钮，和抽屉最右上角间距为24px
.ant-drawer-close {
  margin-inline-end: 0;
  padding-right: 0;
}
.ant-drawer .ant-drawer-close{
  margin-inline-end: 0;
}
.ant-drawer {
  .ant-tabs {
    font-size: 14px;
  }
  // 去掉抽屉顶部和底部的黑线
  .ant-drawer-header {
    border-bottom: none;
  }
  .ant-drawer-footer {
    height: 80px;
    border-top: none;
    padding: 8px 24px;
  }
}

// 新建按钮统一样式
.kl-create-btn {
  background: linear-gradient(90deg, #00cada 0%, #00a2f4 100%);
  border: none;
  &:hover {
    background: linear-gradient(90deg, #2ddae7 0%, #26acf0 100%);
  }
}

// 内容标题统一样式
.content-title {
  margin-bottom: 16px;
  font-weight: 600;
  font-size: 18px;
  color: #00141a;
}
//a-alert
.ant-alert {
  border-radius: @jt-border-radius;
  color: @jt-text-color-primary-opacity07;
  .ant-alert-icon {
    width: 16px;
    margin-right: 12px;
  }
  &.ant-alert-info {
    background-color: #eafbff;
    border-color: #8ddbeb;
    .ant-alert-icon {
      color: #00a0cc;
    }
  }
  &.ant-alert-warning {
    background-color: #fef8ed;
    border-color: #fdd8a3;
    .ant-alert-icon {
      color: #f9881b;
    }
  }
}

.ant-steps {
  width: 884px;
  height: 32px;
}
//select 下拉选中
.ant-select-dropdown .ant-select-item-option-selected:not(.ant-select-item-option-disabled) {
  color: @jt-primary-color;
  font-weight: @jt-font-weight-medium;
  background-color: #e7fbff;
}

// alert图标始终在左上角
.alert-icon-flex-start {
  margin-bottom: 16px;
  align-items: flex-start;
  .ant-alert-icon {
    margin-top: 4px;
  }
}
// 覆盖tooltip文字样式
.ant-tooltip .ant-tooltip-inner {
  color: @jt-text-color-primary-opacity07;
  border-radius: 2px;
}

.ant-table-container {
  // 选中时去掉默认样式
  .ant-table-tbody > tr.ant-table-row-selected > td {
    background: white;
  }
  .ant-table-tbody > tr.ant-table-row-selected:hover > td,
  .ant-table-tbody > tr.ant-table-row:hover > td {
    background: #fafafb;
  }
  // 设置table header和body的上下padding间距，总高度46px
  .ant-table-thead > tr > th {
    padding-top: 12px;
    padding-bottom: 12px;
    background: #fafafb;
    // 去掉头部间隔的竖线
    &::before {
      display: none;
    }
  }
  // td总高度46px
  .ant-table-tbody > tr > td {
    padding-top: 11px;
    padding-bottom: 11px;
  }
}

// 去掉table空态时下面多余的线
.ant-table-placeholder td {
  border-bottom: none !important;
}

// notification样式全局覆盖
.ant-notification-notice {
  .ant-notification-notice-message {
    font-weight: @jt-font-weight-medium;
  }
  .ant-notification-notice-close {
    width: 16px;
    height: 16px;
  }
  .ant-notification-notice-description{
    word-break: break-all;
  }
}

// 弹框
.ant-modal {
  .ant-modal-content {
    padding-top: 16px;
    padding-bottom: 24px;
    .ant-modal-close {
      width: 16px;
      height: 16px;
      right: 24px;
      .ant-modal-close-x {
        font-size: 14px;
        line-height: 16px;
      }
    }
    .ant-btn-primary:not(:disabled):not(.ant-btn-dangerous) {
      background: linear-gradient(90deg, #00cada 0%, #00a2f4 100%);
      background-origin: border-box;
    }
    .ant-btn-primary:not(:disabled):not(.ant-btn-dangerous):hover {
      background: linear-gradient(90deg, #2ddae7 0%, #26acf0 100%);
      background-origin: border-box;
    }
  }
  .ant-modal-header {
    .ant-modal-title {
      color: #00141a;
      line-height: 24px;
    }
  }
  .ant-modal-footer{
    .ant-btn>span{
      letter-spacing: -2px;
    }
  }
}

.ant-input-number {
  border-radius: 2px;
}
.ant-input-number .ant-input-number-input {
  border-radius: 2px;
}
// input-number后缀字符无边框和底色
// .ant-input-number-group-wrapper{
//   &.no-addon-after-wrapper {
//     width: 100%;
//     .no-addon-after-wrapper {
//       border-right: transparent;
//     }
//     .ant-input-number-group-addon {
//       background-color: #fff;
//     }
//     .ant-input-number-focused + .ant-input-number-group-addon {
//       border-color: @jt-input-border-hover-color;
//     }
//     .ant-input-number-status-error + .ant-input-number-group-addon {
//       border-color: @jt-input-border-error-color;
//     }
//     :hover {
//       &:not(.ant-input-number-status-error){
//         .no-addon-after-wrapper {
//           border-color: @jt-input-border-hover-color;
//           box-shadow: none;
//         }
//         .ant-input-number-group-addon {
//           border-color: @jt-input-border-hover-color;
//         }
//       }
//     }
//   }
// }
.ant-input-number-group-wrapper{
  &.no-addon-after-wrapper{
    >.ant-input-number-wrapper{
      >.ant-input-number-group-addon{
        background-color: #fff;
      }
      // 正常状态
      >.ant-input-number-focused{
        box-shadow: none;
        +.ant-input-number-group-addon{
          border-color: @jt-input-border-hover-color;
        }
      }
      &:hover{
        >.ant-input-number{
          border-color: @jt-input-border-hover-color;
          +.ant-input-number-group-addon{
            border-color: @jt-input-border-hover-color;
          }
        }
      }
      // error状态
      >.ant-input-number{
        border-right: none;
        &.ant-input-number-status-error{
          +.ant-input-number-group-addon{
            border-left: none;
            border-color: @jt-input-border-error-color;
          }
        }
      }
      &:hover{
        >.ant-input-number-status-error{
          border-right: none;
          border-color: @jt-input-border-hover-error-color;
          +.ant-input-number-group-addon{
            border-left: none;
            border-color: @jt-input-border-hover-error-color;
          }
        }
      }
    }
  }
}

.ant-dropdown-menu {
  .ant-dropdown-menu-item.ant-dropdown-menu-item-selected,
  .ant-dropdown-menu-item.ant-dropdown-menu-item-selected:hover {
    background-color: #e6f7fa;
  }
}

// tab
.ant-tabs .ant-tabs-tab {
  padding: 4px 12px 14px 12px;
}

.ant-select-multiple .ant-select-selection-item {
  background: rgba(0, 20, 26, 0.02);
}
//全局laoding
.ant-spin-nested-loading .ant-spin-blur {
  opacity: 0.2;
}

// 用于小图标型的icon按钮
.icon-button{
  color: #7f828f;
  cursor: pointer;
  &:hover{
    color: @jt-primary-color;
  }
}

// 蓝色文字按钮悬停的颜色为2BB4D6，例如表格操作，页面中的蓝色入口链接
.ant-btn-link:not(:disabled):hover {
  color: @jt-link-button-hover-color;
}

.card-border-radius {
  border-radius: @jt-card-border-radius;
}

// 按钮文字和icon尺寸调整
.ant-btn {
  font-size: @jt-font-size-base;
  .anticon {
    font-size: @jt-font-size-lg;
  }
  &.ant-btn-default:not(:hover):not(.ant-btn-dangerous) {
    color: @jt-text-color-primary-opacity07;
  }
}

// 去掉按钮阴影
.ant-btn-default {
  box-shadow: none;
}

// 文字溢出省略号
.text-ellipsis {
  display: block;
  width: fit-content;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  &:hover {
    color: @jt-primary-color;
  }
}

// tabs组件，右上角的extra部分和左侧文案顶端对齐（保证与外侧容器的上间距为20px，和下方间隔线的间距为10px）
.ant-tabs {
  .ant-tabs-nav {
    align-items: start;
  }
}

// 去掉table左右上角的圆角
.ant-table-wrapper .ant-table {
  border-radius: 0;
  color: @jt-text-color-primary-opacity07;
}

// 去掉表单标题后的冒号
.ant-form-item .ant-form-item-label > label::after {
  content: ' ';
}

// 去掉表单标题后的冒号
.formLableEnd16 .ant-form-item .ant-form-item-label > label::after {
  content: ' ';
  margin-inline-end: 16px;
}

// 全局radio和selector文字颜色修改
.ant-radio-button-wrapper {
  padding-inline: 24px;
  color: @jt-text-color-primary-opacity07;
}
.ant-radio-button-wrapper .ant-radio-button-wrapper-disabled {
  color: rgba(0, 0, 0, 0.25);
}
.ant-select-selection-item {
  color: @jt-text-color-primary-opacity07;
}
// a-descriptions
.ant-descriptions-item .ant-descriptions-item-content {
  color: #00141a;
}

// 用于table跳转列的hover效果
.route-hover-item {
  cursor: pointer;
  &:hover {
    color: @jt-primary-color;
  }
}
// 更多按钮下拉显示
.more-btn-dropdown-overlay.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item {
  color: rgba(0, 20, 26, 0.7);
  height: 40px;
  padding: 0 8px;
  min-width: 86px;
}
.more-btn-dropdown-overlay.ant-dropdown .ant-dropdown-menu .ant-dropdown-menu-item.ant-dropdown-menu-item-disabled {
  color: rgba(0, 20, 26, 0.25);
}
.more-btn-dropdown-overlay.ant-dropdown .ant-dropdown-menu-item:not(.ant-dropdown-menu-item-disabled):hover {
  background: #e6f7fa;
  color: #00a0cc;
}

.ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:first-child {
  border-start-start-radius: 0;
}
.ant-table-wrapper .ant-table-container table > thead > tr:first-child > *:last-child {
  border-start-end-radius: 0;
}

.ant-table-wrapper .ant-table .ant-table-header {
  border-radius: 0;
}
.ant-table-wrapper .ant-table-thead th.ant-table-column-sort{
  background-color: #fafafb;
}
.ant-table-wrapper td.ant-table-column-sort{
  background-color: transparent;
}

// 日期选择器
.ant-picker {
  &.ant-picker-range {
    border-radius: @jt-border-radius;
  }
}

// tree组件相关
.ant-tree {
  .ant-tree-treenode {
    align-items: center;
    margin-bottom: 8px;
    .ant-tree-switcher {
      width: 16px;
      height: 16px;
      line-height: 16px;
    }
    .ant-tree-node-content-wrapper {
      width: 100%;
      &.ant-tree-node-selected {
        color: @jt-primary-color;
        background: transparent;
        &:hover {
          background: @jt-color-background-layout;
        }
      }
    }
    .ant-tree-switcher {
      align-self: auto;
      margin-right: 3px;
      &:hover:not(.ant-tree-switcher-noop) {
        background: @jt-color-background-layout;
      }
    }
  }
}
/* 卡片默认样式 */
.box-card-style {
  padding: 20px;
  margin-bottom: 20px;
  background-color: @jt-color-white;
  color: @jt-text-color-primary;
  border-radius: 4px;
  box-shadow: 0px 2px 4px 0px rgba(0, 20, 26, 0.04);
  border: 1px solid @jt-color-white;
  backdrop-filter: blur(8px);
}
// 默认间距24

// 文本间距
.textMargin .ant-form-item {
  margin-bottom: 20px;
  .ant-form-item-label > label {
    height: 22px;
  }
  .ant-form-item-control-input {
    min-height: 0;
  }
}
// 输入框间距
.inputMargin .ant-form-item {
  margin-bottom: 32px;
}

// 有图标的按钮，图标和文字垂直居中
.btn-vertical-align {
  display: flex;
  align-items: center;
}
.jt-linethrough-txt {
  text-decoration: line-through;
  color: rgba(0,20,26,0.25);
}

.jt-bdr-4 {
  border-radius: 4px;
}

// 强制折行
.break-all {
  word-break: break-all;
}
